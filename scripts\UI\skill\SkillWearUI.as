package UI.skill
{
   import UI.NormalUICtrl;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripMoveCtrl;
   import UI.bag.ItemsGripTipCtrl;
   import UI.bag.ItemsGripUnlockCtrl;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import dataAll.skill.HeroSkillData;
   import dataAll.skill.define.HeroSkillDefine;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class SkillWearUI extends NormalUI
   {
      public var wearBox:ItemsGripBox = new ItemsGripBox();
      
      public var bagBox:ItemsGripBox = new ItemsGripBox();
      
      private var wearTag:Sprite;
      
      private var bagTag:Sprite;
      
      private var pageBoxTag:Sprite;
      
      public function SkillWearUI()
      {
         super();
         this.wearBox.imgType = "equipGrip";
         this.wearBox.arg.init(5,2,5,5);
         this.wearBox.evt.setWantEvent(true,true,true,true,true,true);
         addChild(this.wearBox);
         this.bagBox.imgType = "equipGrip";
         this.bagBox.arg.init(5,3,5,5);
         this.bagBox.evt.setWantEvent(true,true,true,true,true,true);
         addChild(this.bagBox);
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["wearTag","bagTag","pageBoxTag"];
         super.setImg(img0);
         NormalUICtrl.setTag(this.wearBox,this.wearTag);
         NormalUICtrl.setTag(this.bagBox,this.bagTag);
         this.bagBox.pageBox.x = this.pageBoxTag.x - this.bagBox.x;
         this.bagBox.pageBox.y = this.pageBoxTag.y - this.bagBox.y;
         ItemsGripMoveCtrl.addEvent_byItemsGripBox(this.wearBox);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.wearBox);
         ItemsGripUnlockCtrl.addEvent_byItemsGripBox(this.wearBox);
         ItemsGripMoveCtrl.addEvent_byItemsGripBox(this.bagBox);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.bagBox);
         this.bagBox.addEventListener(ClickEvent.ON_CLICK,this.gripClick);
         this.bagBox.addEventListener(ClickEvent.ON_DOUBLE_CLICK,this.gripDoubleClick);
         this.wearBox.addEventListener(ClickEvent.ON_CLICK,this.gripClick);
         this.wearBox.addEventListener(ClickEvent.ON_DOUBLE_CLICK,this.gripDoubleClick);
         this.bagBox.pageBox.setToNormalBtn();
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      public function fleshData() : void
      {
         this.wearBox.inData_byDataGroup(Gaming.PG.DATA.skill);
         this.wearBox.setAllPro("doubleClickEnabled",true);
         this.wearBox.setAllLevelTxtBySite();
         this.bagBox.inData_byDataGroup(Gaming.PG.DATA.skillBag);
         this.bagBox.setAllPro("doubleClickEnabled",true);
      }
      
      private function gripClick(e:ClickEvent) : void
      {
         var grip0:ItemsGrid = e.child as ItemsGrid;
         var skillUpgradeUI:SkillUpgradeUI = Gaming.uiGroup.skillUI.upgradeBox;
         if(skillUpgradeUI.visible)
         {
            this.gripDoubleClick(e);
         }
         else
         {
            // 显示编辑按钮
            this.showEditButton(grip0);
         }
      }
      
      public function chooseGripByData(data0:HeroSkillData) : ItemsGrid
      {
         var grip0:ItemsGrid = this.bagBox.findGripByData(data0);
         if(Boolean(grip0))
         {
            this.bagBox.setChoose_byIndex(grip0.index);
            this.wearBox.setChoose_byIndex(-1);
         }
         else
         {
            grip0 = this.wearBox.findGripByData(data0);
            if(Boolean(grip0))
            {
               this.bagBox.setChoose_byIndex(-1);
               this.wearBox.setChoose_byIndex(grip0.index);
            }
         }
         return grip0;
      }
      
      private function gripDoubleClick(e:ClickEvent) : void
      {
         var skillUpgradeUI:SkillUpgradeUI = null;
         var grip0:ItemsGrid = e.child as ItemsGrid;
         if(Boolean(grip0.itemsData))
         {
            skillUpgradeUI = Gaming.uiGroup.skillUI.upgradeBox;
            skillUpgradeUI.nowData = grip0.itemsData as HeroSkillData;
            Gaming.uiGroup.skillUI.showBox("upgrade");
            if(e.target == this.bagBox)
            {
               this.bagBox.setChoose_byIndex(e.index);
               this.wearBox.setChoose_byIndex(-1);
            }
            else
            {
               this.bagBox.setChoose_byIndex(-1);
               this.wearBox.setChoose_byIndex(e.index);
            }
         }
      }

      private var editButton:NormalBtn = null;
      private var currentEditGrip:ItemsGrid = null;

      private function showEditButton(grip0:ItemsGrid) : void
      {
         if(!grip0 || !grip0.itemsData)
         {
            this.hideEditButton();
            return;
         }

         this.currentEditGrip = grip0;

         // 创建编辑按钮（如果不存在）
         if(!this.editButton)
         {
            this.editButton = new NormalBtn();
            this.editButton.setName("编辑");
            this.editButton.addEventListener(MouseEvent.CLICK, this.editButtonClick);
            addChild(this.editButton);
         }

         // 设置按钮位置（在技能图标右上角）
         this.editButton.x = grip0.x + grip0.width - 30;
         this.editButton.y = grip0.y;
         this.editButton.visible = true;
      }

      private function hideEditButton() : void
      {
         if(this.editButton)
         {
            this.editButton.visible = false;
         }
         this.currentEditGrip = null;
      }

      private function editButtonClick(e:MouseEvent) : void
      {
         if(!this.currentEditGrip || !this.currentEditGrip.itemsData)
         {
            return;
         }

         var skillData:HeroSkillData = this.currentEditGrip.itemsData as HeroSkillData;
         if(skillData)
         {
            this.showSkillEditDialog(skillData);
         }
      }

      private function showSkillEditDialog(skillData:HeroSkillData) : void
      {
         var skillDefine:HeroSkillDefine = skillData.save.getDefine();
         var currentLevel:int = skillData.save.lv;
         var maxLevel:int = skillDefine.getMaxLevel();

         var editText:String = "编辑技能: " + skillDefine.cnName + "\n";
         editText += "当前等级: " + currentLevel + "/" + maxLevel + "\n\n";
         editText += "请选择操作:\n";
         editText += "1. 设置等级 (输入数字)\n";
         editText += "2. 满级 (输入 max)\n";
         editText += "3. 重置 (输入 reset)";

         Gaming.uiGroup.alertBox.textInput.showTextInput(editText, currentLevel.toString(), this.processSkillEdit, "yesAndNo", 999);
      }

      private function processSkillEdit(inputValue:String) : void
      {
         if(!this.currentEditGrip || !this.currentEditGrip.itemsData)
         {
            Gaming.uiGroup.alertBox.showError("没有选中的技能");
            return;
         }

         var skillData:HeroSkillData = this.currentEditGrip.itemsData as HeroSkillData;
         var skillDefine:HeroSkillDefine = skillData.save.getDefine();
         var maxLevel:int = skillDefine.getMaxLevel();

         try
         {
            if(inputValue.toLowerCase() == "max")
            {
               // 设置为满级
               skillData.save.lv = maxLevel;
               Gaming.uiGroup.alertBox.showSuccess("技能 " + skillDefine.cnName + " 已设置为满级 (" + maxLevel + ")");
            }
            else if(inputValue.toLowerCase() == "reset")
            {
               // 重置为1级
               skillData.save.lv = 1;
               Gaming.uiGroup.alertBox.showSuccess("技能 " + skillDefine.cnName + " 已重置为1级");
            }
            else
            {
               // 设置为指定等级
               var newLevel:int = parseInt(inputValue);
               if(isNaN(newLevel) || newLevel < 1)
               {
                  Gaming.uiGroup.alertBox.showError("请输入有效的等级数字 (1-" + maxLevel + ")");
                  return;
               }

               if(newLevel > maxLevel)
               {
                  newLevel = maxLevel;
               }

               skillData.save.lv = newLevel;
               Gaming.uiGroup.alertBox.showSuccess("技能 " + skillDefine.cnName + " 等级已设置为 " + newLevel);
            }

            // 刷新显示
            this.fleshData();
            this.hideEditButton();
         }
         catch(e:Error)
         {
            Gaming.uiGroup.alertBox.showError("编辑技能失败: " + e.message);
         }
      }
   }
}

