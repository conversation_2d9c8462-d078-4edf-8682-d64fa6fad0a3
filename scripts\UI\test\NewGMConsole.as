package UI.test
{
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import flash.display.DisplayObject;
   import flash.display.Graphics;
   import flash.display.Shape;
   import flash.display.Sprite;
   import flash.events.KeyboardEvent;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   import flash.text.TextFieldType;
   import flash.text.TextFormat;
   import flash.ui.Keyboard;
   import flash.net.FileReference;
   import flash.system.System;
   import flash.text.TextFieldType;
   import utils.ClassProperty;
   import utils.ObjectToXml;
   import com.greensock.TweenLite;
   import utils.JSON2;
   import dataAll._player.PlayerDataSupple;
   import UI.count.CountCtrl;
   import dataAll.gift.GiftAddit;

   public class NewGMConsole extends Sprite
   {
      private var background:Shape;
      private var titleText:TextField;
      private var outputText:TextField;
      private var closeBtn:Sprite;

      private var isVisible:Boolean = false;
      private var currentCategory:String = "玩家";

      // 分类标签按钮
      private var categoryBtns:Array = [];
      private var categoryNames:Array = ["玩家", "装备", "关卡", "物品", "系统", "存档", "任务", "宠物", "竞技", "军团", "成就", "时间", "自定义", "其他"];
      private var categoryColors:Array = [0xFF6600, 0x6600FF, 0x00FF66, 0xFF0066, 0x0066FF, 0xFF9900, 0x9900FF, 0x00FF99, 0xFF0099, 0xFF6699, 0x66FF99, 0xFF3366, 0xFF1493, 0x9999FF];

      // 功能按钮容器
      private var buttonContainer:Sprite;
      private var functionBtns:Array = [];

      // 输入框相关
      private var inputContainer:Sprite;
      private var inputBox:TextField;
      private var inputLabel:TextField;
      private var inputBtn:Sprite;
      private var currentInputCommand:String = "";
      
      public function NewGMConsole()
      {
         super();
         this.createUI();
         this.setupEventListeners();
         this.visible = false;
      }

      private function createUI():void
      {
         // 创建背景 - 二次元风格
         background = new Shape();
         var g:Graphics = background.graphics;

         // 粉色渐变背景
         g.beginFill(0xfff0f8, 0.95);  // 浅粉色背景
         g.drawRoundRect(0, 0, 900, 600, 25, 25);
         g.endFill();

         // 可爱的多彩边框
         g.lineStyle(4, 0xff69b4, 1);  // 粉色主边框
         g.drawRoundRect(0, 0, 900, 600, 25, 25);
         g.lineStyle(2, 0x87ceeb, 0.8);  // 天蓝色内边框
         g.drawRoundRect(3, 3, 894, 594, 22, 22);
         g.lineStyle(1, 0xffd700, 0.9);  // 金色装饰边框
         g.drawRoundRect(6, 6, 888, 588, 19, 19);

         // 添加可爱的装饰点
         drawCuteDecorations(g);

         addChild(background);

         // 标题栏 - 二次元风格
         var titleBg:Shape = new Shape();
         var titleG:Graphics = titleBg.graphics;
         titleG.beginFill(0xff69b4, 0.9);  // 粉色标题栏
         titleG.drawRoundRect(0, 0, 900, 50, 25, 25);
         titleG.endFill();
         titleG.beginFill(0xffc0cb, 0.7);  // 浅粉色渐变
         titleG.drawRect(0, 40, 900, 10);
         titleG.endFill();

         // 添加星星装饰
         drawStarDecorations(titleG);
         addChild(titleBg);

         // 标题文字 - 可爱风格
         titleText = createTextField("✨ GM魔法控制台 ✨ - 按~键关闭喵~ 挚爱小龙制作-请勿乱传制作不易", 20, 0xffffff, true);
         titleText.x = 20;
         titleText.y = 15;
         titleText.width = 800;
         addChild(titleText);

         // 关闭按钮 - 二次元风格
         closeBtn = createAnimeButton("❌", 0xff6b9d, 40, 40);
         closeBtn.x = 850;
         closeBtn.y = 5;
         addChild(closeBtn);

         // 创建分类标签
         createCategoryTabs();

         // 创建按钮容器
         buttonContainer = new Sprite();
         buttonContainer.x = 15;
         buttonContainer.y = 95;
         addChild(buttonContainer);

         // 输入框容器
         inputContainer = new Sprite();
         inputContainer.x = 15;
         inputContainer.y = 405;
         addChild(inputContainer);
         createInputBox();

         // 输出文本框 - 二次元风格
         outputText = createTextField("", 14, 0x4a4a4a, false);
         outputText.x = 20;
         outputText.y = 460;
         outputText.width = 860;
         outputText.height = 130;
         outputText.border = true;
         outputText.borderColor = 0xff69b4;
         outputText.background = true;
         outputText.backgroundColor = 0xfff8fc;  // 浅粉色背景
         outputText.multiline = true;
         outputText.wordWrap = true;
         addChild(outputText);

         // 显示默认分类
         showCategory("玩家");

         // 初始化输出 - 二次元风格
         addOutput("✨ ═══ GM魔法控制台启动成功喵~ ═══ ✨");
         addOutput("🌸 欢迎来到二次元世界的管理后台~");
         addOutput("💖 点击上方可爱的分类标签选择功能喵");
         addOutput("⭐ 点击功能按钮释放GM魔法~");
         addOutput("🎀 部分魔法需要输入参数才能生效哦~");
      }

      private function createInputBox():void
      {
         // 输入标签 - 二次元风格
         inputLabel = createTextField("🌸 请输入数值/名称喵~:", 14, 0xff1493, true);
         inputLabel.x = 0;
         inputLabel.y = 3;
         inputLabel.width = 160;
         inputLabel.height = 25;
         inputContainer.addChild(inputLabel);

         // 输入框 - 二次元风格
         inputBox = createTextField("", 14, 0x4a4a4a, false);
         inputBox.x = 165;
         inputBox.y = 0;
         inputBox.width = 240;
         inputBox.height = 30;
         inputBox.border = true;
         inputBox.borderColor = 0xff69b4;
         inputBox.background = true;
         inputBox.backgroundColor = 0xfff8fc;  // 浅粉色背景
         inputBox.type = TextFieldType.INPUT;
         inputBox.maxChars = 50;
         inputContainer.addChild(inputBox);

         // 确认按钮 - 二次元风格
         inputBtn = createAnimeButton("💖 确认", 0xff69b4, 90, 30);
         inputBtn.x = 415;
         inputBtn.y = 0;
         inputBtn.addEventListener(MouseEvent.CLICK, onInputConfirm);
         inputContainer.addChild(inputBtn);

         // 默认隐藏
         inputContainer.visible = false;
      }
      
      private function createCategoryTabs():void
      {
         var tabY:int = 55;
         var tabWidth:int = 62;
         var tabSpacing:int = 64;

         for(var i:int = 0; i < categoryNames.length; i++)
         {
            var tab:Sprite = createAnimeTabButton(categoryNames[i], getAnimeColor(i), tabWidth, 42);
            tab.x = 20 + i * tabSpacing;
            tab.y = tabY;
            tab.name = categoryNames[i];
            addChild(tab);
            categoryBtns.push(tab);
         }

         // 默认选中第一个
         updateTabSelection("玩家");
      }

      private function updateTabSelection(selectedCategory:String):void
      {
         for(var i:int = 0; i < categoryBtns.length; i++)
         {
            var tab:Sprite = categoryBtns[i];
            var isSelected:Boolean = (tab.name == selectedCategory);

            // 重绘标签样式
            var g:Graphics = tab.graphics;
            g.clear();
            if(isSelected)
            {
               g.beginFill(categoryColors[i], 1);
               g.lineStyle(2, 0xFFFFFF, 1);
            }
            else
            {
               g.beginFill(categoryColors[i], 0.7);
               g.lineStyle(1, 0x888888, 1);
            }
            g.drawRoundRect(0, 0, 65, 35, 8, 8);
            g.endFill();
         }
      }
      
      private function createTextField(text:String, size:int, color:uint, bold:Boolean):TextField
      {
         var tf:TextField = new TextField();
         var format:TextFormat = new TextFormat();
         format.font = "Microsoft YaHei";  // 更好的中文字体
         format.size = size;
         format.color = color;
         format.bold = bold;
         format.leading = 3;  // 增加行间距
         tf.defaultTextFormat = format;
         tf.text = text;
         tf.selectable = false;
         tf.antiAliasType = "advanced";  // 抗锯齿
         tf.gridFitType = "pixel";       // 像素对齐
         return tf;
      }

      // 添加可爱装饰
      private function drawCuteDecorations(g:Graphics):void
      {
         // 绘制小星星装饰
         for(var i:int = 0; i < 8; i++) {
            var x:Number = 50 + i * 100 + Math.random() * 50;
            var y:Number = 50 + Math.random() * 500;
            drawStar(g, x, y, 8, 0xffd700, 0.6);
         }

         // 绘制小心心装饰
         for(var j:int = 0; j < 6; j++) {
            var hx:Number = 100 + j * 120 + Math.random() * 60;
            var hy:Number = 80 + Math.random() * 400;
            drawHeart(g, hx, hy, 6, 0xff69b4, 0.4);
         }
      }

      // 绘制星星装饰
      private function drawStarDecorations(g:Graphics):void
      {
         for(var i:int = 0; i < 12; i++) {
            var x:Number = 60 + i * 70;
            var y:Number = 10 + Math.random() * 20;
            drawStar(g, x, y, 5, 0xffffff, 0.8);
         }
      }

      // 绘制星星
      private function drawStar(g:Graphics, x:Number, y:Number, size:Number, color:uint, alpha:Number):void
      {
         g.beginFill(color, alpha);
         g.moveTo(x, y - size);
         g.lineTo(x + size * 0.3, y - size * 0.3);
         g.lineTo(x + size, y);
         g.lineTo(x + size * 0.3, y + size * 0.3);
         g.lineTo(x, y + size);
         g.lineTo(x - size * 0.3, y + size * 0.3);
         g.lineTo(x - size, y);
         g.lineTo(x - size * 0.3, y - size * 0.3);
         g.lineTo(x, y - size);
         g.endFill();
      }

      // 绘制爱心
      private function drawHeart(g:Graphics, x:Number, y:Number, size:Number, color:uint, alpha:Number):void
      {
         g.beginFill(color, alpha);
         g.moveTo(x, y + size);
         g.curveTo(x - size, y - size/2, x - size/2, y - size/2);
         g.curveTo(x, y - size, x + size/2, y - size/2);
         g.curveTo(x + size, y - size/2, x, y + size);
         g.endFill();
      }

      // 获取二次元配色
      private function getAnimeColor(index:int):uint
      {
         var animeColors:Array = [
            0xff69b4,  // 粉色
            0x87ceeb,  // 天蓝色
            0x98fb98,  // 浅绿色
            0xffd700,  // 金色
            0xdda0dd,  // 紫色
            0xff6347,  // 橙红色
            0x40e0d0,  // 青色
            0xf0e68c,  // 卡其色
            0xff1493,  // 深粉色
            0x9370db,  // 中紫色
            0x00ced1,  // 深青色
            0xffa500,  // 橙色
            0xda70d6,  // 兰花紫
            0x32cd32   // 酸橙绿
         ];
         return animeColors[index % animeColors.length];
      }

      private function createButton(text:String, color:uint, width:int = 70, height:int = 25):Sprite
      {
         var btn:Sprite = new Sprite();
         var g:Graphics = btn.graphics;
         g.beginFill(color, 0.8);
         g.drawRoundRect(0, 0, width, height, 5, 5);
         g.endFill();
         g.lineStyle(1, 0xFFFFFF, 1);
         g.drawRoundRect(0, 0, width, height, 5, 5);

         var tf:TextField = createTextField(text, 12, 0xFFFFFF, true);
         tf.x = 5;
         tf.y = (height - 15) / 2;
         tf.width = width - 10;
         tf.height = 15;
         btn.addChild(tf);

         btn.buttonMode = true;
         btn.mouseChildren = false;
         return btn;
      }

      private function createTabButton(text:String, color:uint, width:int, height:int):Sprite
      {
         var btn:Sprite = new Sprite();
         var g:Graphics = btn.graphics;
         g.beginFill(color, 0.7);
         g.drawRoundRect(0, 0, width, height, 8, 8);
         g.endFill();
         g.lineStyle(1, 0x888888, 1);
         g.drawRoundRect(0, 0, width, height, 8, 8);

         var tf:TextField = createTextField(text, 12, 0xFFFFFF, true);
         tf.x = 5;
         tf.y = (height - 16) / 2;
         tf.width = width - 10;
         tf.height = 18;
         btn.addChild(tf);

         btn.buttonMode = true;
         btn.mouseChildren = false;
         return btn;
      }

      // 现代化标签按钮
      private function createModernTabButton(text:String, color:uint, width:int, height:int):Sprite
      {
         var btn:Sprite = new Sprite();
         var g:Graphics = btn.graphics;

         // 渐变背景
         g.beginFill(color, 0.85);
         g.drawRoundRect(0, 0, width, height, 10, 10);
         g.endFill();

         // 高光边框
         g.lineStyle(2, 0xffffff, 0.3);
         g.drawRoundRect(1, 1, width-2, height-2, 9, 9);

         // 内阴影效果
         g.lineStyle(1, 0x000000, 0.2);
         g.drawRoundRect(0, 0, width, height, 10, 10);

         var tf:TextField = createTextField(text, 12, 0xFFFFFF, true);
         tf.x = 3;
         tf.y = (height - 16) / 2;
         tf.width = width - 6;
         btn.addChild(tf);

         btn.buttonMode = true;
         btn.mouseChildren = false;
         return btn;
      }

      // 二次元风格按钮
      private function createAnimeButton(text:String, color:uint, width:int, height:int):Sprite
      {
         var btn:Sprite = new Sprite();
         var g:Graphics = btn.graphics;

         // 可爱的圆角背景
         g.beginFill(color, 0.9);
         g.drawRoundRect(0, 0, width, height, 15, 15);
         g.endFill();

         // 高光效果
         g.beginFill(0xffffff, 0.4);
         g.drawRoundRect(3, 3, width-6, height/2-3, 12, 12);
         g.endFill();

         // 可爱的边框
         g.lineStyle(2, 0xffffff, 0.8);
         g.drawRoundRect(0, 0, width, height, 15, 15);

         // 内部装饰边框
         g.lineStyle(1, 0xffd700, 0.6);
         g.drawRoundRect(2, 2, width-4, height-4, 13, 13);

         var tf:TextField = createTextField(text, 12, 0xffffff, true);
         tf.x = 5;
         tf.y = (height - 16) / 2;
         tf.width = width - 10;
         btn.addChild(tf);

         btn.buttonMode = true;
         btn.mouseChildren = false;
         return btn;
      }

      // 二次元标签按钮
      private function createAnimeTabButton(text:String, color:uint, width:int, height:int):Sprite
      {
         var btn:Sprite = new Sprite();
         var g:Graphics = btn.graphics;

         // 可爱的渐变背景
         g.beginFill(color, 0.85);
         g.drawRoundRect(0, 0, width, height, 12, 12);
         g.endFill();

         // 高光效果
         g.beginFill(0xffffff, 0.3);
         g.drawRoundRect(2, 2, width-4, height/2-2, 10, 10);
         g.endFill();

         // 可爱的边框
         g.lineStyle(2, 0xffffff, 0.7);
         g.drawRoundRect(0, 0, width, height, 12, 12);

         // 装饰小星星
         drawStar(g, width-10, 8, 3, 0xffffff, 0.8);

         var tf:TextField = createTextField(text, 11, 0xffffff, true);
         tf.x = 3;
         tf.y = (height - 16) / 2;
         tf.width = width - 6;
         btn.addChild(tf);

         btn.buttonMode = true;
         btn.mouseChildren = false;
         return btn;
      }

      private function createFunctionButton(text:String, description:String, color:uint = 0xff69b4):Sprite
      {
         var btn:Sprite = new Sprite();
         var g:Graphics = btn.graphics;

         // 二次元可爱背景
         g.beginFill(color, 0.9);
         g.drawRoundRect(0, 0, 210, 40, 15, 15);
         g.endFill();

         // 高光效果
         g.beginFill(0xffffff, 0.3);
         g.drawRoundRect(3, 3, 204, 18, 12, 12);
         g.endFill();

         // 可爱边框
         g.lineStyle(2, 0xffffff, 0.8);
         g.drawRoundRect(0, 0, 210, 40, 15, 15);

         // 装饰边框
         g.lineStyle(1, 0xffd700, 0.6);
         g.drawRoundRect(2, 2, 206, 36, 13, 13);

         // 添加小装饰
         drawHeart(g, 195, 10, 4, 0xffffff, 0.7);

         var tf:TextField = createTextField(text, 12, 0xffffff, true);
         tf.x = 10;
         tf.y = 12;
         tf.width = 185;
         tf.height = 20;
         btn.addChild(tf);

         btn.buttonMode = true;
         btn.mouseChildren = false;
         btn.name = description; // 存储描述用于执行

         // 添加悬停效果
         btn.addEventListener(MouseEvent.MOUSE_OVER, onAnimeFunctionButtonOver);
         btn.addEventListener(MouseEvent.MOUSE_OUT, onAnimeFunctionButtonOut);

         return btn;
      }
      
      private function setupEventListeners():void
      {
         closeBtn.addEventListener(MouseEvent.CLICK, hide);
         closeBtn.addEventListener(MouseEvent.MOUSE_OVER, onCloseButtonOver);
         closeBtn.addEventListener(MouseEvent.MOUSE_OUT, onCloseButtonOut);

         // 分类标签事件
         for(var i:int = 0; i < categoryBtns.length; i++)
         {
            categoryBtns[i].addEventListener(MouseEvent.CLICK, onCategoryClick);
            categoryBtns[i].addEventListener(MouseEvent.MOUSE_OVER, onTabMouseOver);
            categoryBtns[i].addEventListener(MouseEvent.MOUSE_OUT, onTabMouseOut);
         }
      }

      private function onCategoryClick(e:MouseEvent):void
      {
         var categoryName:String = e.currentTarget.name;
         showCategory(categoryName);
      }

      // 二次元关闭按钮悬停效果
      private function onCloseButtonOver(e:MouseEvent):void
      {
         var btn:Sprite = e.currentTarget as Sprite;
         btn.scaleX = btn.scaleY = 1.15;
         btn.alpha = 0.9;
         btn.rotation = 15; // 可爱的旋转效果
      }

      private function onCloseButtonOut(e:MouseEvent):void
      {
         var btn:Sprite = e.currentTarget as Sprite;
         btn.scaleX = btn.scaleY = 1.0;
         btn.alpha = 1.0;
         btn.rotation = 0;
      }

      // 二次元标签悬停效果
      private function onTabMouseOver(e:MouseEvent):void
      {
         var tab:Sprite = e.currentTarget as Sprite;
         if(tab.name != currentCategory) {
            tab.alpha = 0.9;
            tab.scaleX = tab.scaleY = 1.08;
            tab.y -= 2; // 轻微上浮效果
         }
      }

      private function onTabMouseOut(e:MouseEvent):void
      {
         var tab:Sprite = e.currentTarget as Sprite;
         if(tab.name != currentCategory) {
            tab.alpha = 1.0;
            tab.scaleX = tab.scaleY = 1.0;
            tab.y += 2; // 恢复位置
         }
      }

      // 二次元功能按钮悬停效果
      private function onAnimeFunctionButtonOver(e:MouseEvent):void
      {
         var btn:Sprite = e.currentTarget as Sprite;
         btn.scaleX = btn.scaleY = 1.08;
         btn.alpha = 0.95;

         // 重绘按钮添加魔法发光效果
         var g:Graphics = btn.graphics;
         g.clear();

         // 魔法光环
         g.beginFill(0xffd700, 0.3);
         g.drawRoundRect(-3, -3, 216, 46, 18, 18);
         g.endFill();

         // 主背景
         g.beginFill(0xff69b4, 1.0);
         g.drawRoundRect(0, 0, 210, 40, 15, 15);
         g.endFill();

         // 强化高光
         g.beginFill(0xffffff, 0.5);
         g.drawRoundRect(3, 3, 204, 18, 12, 12);
         g.endFill();

         // 发光边框
         g.lineStyle(3, 0xffd700, 1);
         g.drawRoundRect(0, 0, 210, 40, 15, 15);

         // 内部装饰
         g.lineStyle(1, 0xffffff, 0.8);
         g.drawRoundRect(2, 2, 206, 36, 13, 13);

         // 添加闪烁的小星星
         for(var i:int = 0; i < 3; i++) {
            var sx:Number = 20 + i * 60 + Math.random() * 40;
            var sy:Number = 8 + Math.random() * 24;
            drawStar(g, sx, sy, 3, 0xffffff, 0.9);
         }
      }

      private function onAnimeFunctionButtonOut(e:MouseEvent):void
      {
         var btn:Sprite = e.currentTarget as Sprite;
         btn.scaleX = btn.scaleY = 1.0;
         btn.alpha = 1.0;

         // 恢复原始二次元样式
         var g:Graphics = btn.graphics;
         g.clear();

         // 原背景
         g.beginFill(0xff69b4, 0.9);
         g.drawRoundRect(0, 0, 210, 40, 15, 15);
         g.endFill();

         // 高光效果
         g.beginFill(0xffffff, 0.3);
         g.drawRoundRect(3, 3, 204, 18, 12, 12);
         g.endFill();

         // 边框
         g.lineStyle(2, 0xffffff, 0.8);
         g.drawRoundRect(0, 0, 210, 40, 15, 15);

         // 装饰边框
         g.lineStyle(1, 0xffd700, 0.6);
         g.drawRoundRect(2, 2, 206, 36, 13, 13);

         // 恢复小爱心装饰
         drawHeart(g, 195, 10, 4, 0xffffff, 0.7);
      }

      private function showCategory(categoryName:String):void
      {
         currentCategory = categoryName;
         updateTabSelection(categoryName);
         createFunctionButtons(categoryName);
      }

      private function createFunctionButtons(category:String):void
      {
         // 清空现有按钮
         while(buttonContainer.numChildren > 0)
         {
            buttonContainer.removeChildAt(0);
         }
         functionBtns = [];

         var btnWidth:int = 210;
         var btnHeight:int = 40;
         var btnSpacingX:int = 220;
         var btnSpacingY:int = 48;
         var btnsPerRow:int = 4;

         var buttons:Array = getFunctionButtons(category);

         addOutput("正在创建 " + category + " 分类的 " + buttons.length + " 个按钮");

         for(var i:int = 0; i < buttons.length; i++)
         {
            var btnData:Object = buttons[i];
            var btn:Sprite = createFunctionButton(btnData.text, btnData.command, btnData.color || 0x006600);

            var row:int = Math.floor(i / btnsPerRow);
            var col:int = i % btnsPerRow;

            btn.x = col * btnSpacingX;
            btn.y = row * btnSpacingY;

            btn.addEventListener(MouseEvent.CLICK, onFunctionClick);
            buttonContainer.addChild(btn);
            functionBtns.push(btn);
         }

         addOutput("按钮创建完成，共 " + functionBtns.length + " 个");
      }

      private function getFunctionButtons(category:String):Array
      {
         switch(category)
         {
            case "玩家":
               return [
                  {text: "等级+1", command: "player_addlevel_1", color: 0x006600},
                  {text: "等级+10", command: "player_addlevel_10", color: 0x006600},
                  {text: "等级100", command: "player_level_100", color: 0x008800},
                  {text: "满级999", command: "player_level_999", color: 0x00AA00},
                  {text: "自定义等级", command: "player_level_custom", color: 0x0066FF},
                  {text: "经验+1000", command: "player_exp_1000", color: 0x006600},
                  {text: "经验+10000", command: "player_exp_10000", color: 0x008800},
                  {text: "自定义经验", command: "player_exp_custom", color: 0x0066FF},
                  {text: "银币+1000", command: "player_coin_1000", color: 0x006600},
                  {text: "银币+99999", command: "player_coin_99999", color: 0x008800},
                  {text: "自定义银币", command: "player_coin_custom", color: 0x0066FF},
                  {text: "积分+1000", command: "player_score_1000", color: 0x006600},
                  {text: "积分+50000", command: "player_score_50000", color: 0x008800},
                  {text: "自定义积分", command: "player_score_custom", color: 0x0066FF},
                  {text: "解除作弊", command: "player_nocheat", color: 0xFF6600},
                  {text: "开启新队友", command: "player_newpartner", color: 0x0066FF},
                  {text: "巅峰等级+10", command: "player_addpeaklv_10", color: 0x006600},
                  {text: "巅峰经验+1000", command: "player_peakexp_1000", color: 0x008800},
                  {text: "设置称号", command: "player_title", color: 0x00AA00},
                  {text: "获取全部称号", command: "player_allheads", color: 0x00CC00},
                  {text: "修改昵称", command: "player_nickname", color: 0x0066FF, needInput: true}
               ];
            case "装备":
               return [
                  {text: "自定义添加装备", command: "equip_custom", color: 0xFF6600, needInput: true},
                  {text: "添加最强套装", command: "equip_bestsuits", color: 0x6600FF},
                  {text: "添加所有黑色武器", command: "equip_blackarms", color: 0xFF0066},
                  {text: "装备碎片+999", command: "equip_chip_999", color: 0x00AA00},
                  {text: "武器碎片+999", command: "equip_armschip_999", color: 0x008800},
                  {text: "添加所有时装", command: "equip_fashion", color: 0x0066FF},
                  {text: "添加所有装置", command: "equip_device_10", color: 0x006600},
                  {text: "添加所有兵器", command: "equip_weapon_10", color: 0x008800},
                  {text: "武器升级到99", command: "equip_upgradearms", color: 0x00AA00},
                  {text: "清空装备背包", command: "equip_clear", color: 0xFF6600},
                  {text: "清空武器背包", command: "arms_clear", color: 0xFF3300},
                  {text: "扩展装备格子", command: "equip_expandbag", color: 0x9966FF},
                  {text: "扩展武器格子", command: "arms_expandbag", color: 0x6699FF}
               ];
            case "关卡":
               return [
                  {text: "🗼 一键通关虚天塔", command: "tower_complete_all", color: 0x87CEEB},
                  {text: "胜利当前关卡", command: "level_win", color: 0x00FF66},
                  {text: "重启当前关卡", command: "level_restart", color: 0xFF6600},
                  {text: "解锁所有地图", command: "map_unlockall", color: 0x00AA00},
                  {text: "通关所有地图", command: "map_winall", color: 0x0066FF},
                  {text: "解锁所有秘境", command: "map_unlockwilder", color: 0x006600},
                  {text: "秘境钥匙=10", command: "map_wilderkey_10", color: 0x008800},
                  {text: "扫荡次数=10", command: "map_sweep_10", color: 0x00AA00},
                  {text: "扫荡次数=99", command: "map_sweep_99", color: 0x0066FF},
                  {text: "Boss血量50%", command: "level_bosslife_50", color: 0xFF6600},
                  {text: "Boss血量1%", command: "level_bosslife_1", color: 0xFF0066},
                  {text: "杀死Boss", command: "level_killboss", color: 0xFF0000},
                  {text: "测试敌人开关", command: "level_testenemy", color: 0x006600}
               ];
            case "物品":
               return [
                  {text: "自定义添加物品", command: "item_custom", color: 0xFF6600, needInput: true},
                  {text: "所有食材+99", command: "food_raw_99", color: 0x00AA00},
                  {text: "清空物品背包", command: "things_clear", color: 0xFF6600},
                  {text: "清空基因背包", command: "gene_clear", color: 0xFF3300},
                  {text: "清空零件背包", command: "parts_clear", color: 0xFF0066},
                  {text: "清空技能背包", command: "skill_clear", color: 0xFF0033},
                  {text: "武器背包=200", command: "bag_arms_200", color: 0x006600},
                  {text: "装备背包=200", command: "bag_equip_200", color: 0x008800},
                  {text: "物品背包=200", command: "bag_things_200", color: 0x00AA00},
                  {text: "基因背包=200", command: "bag_gene_200", color: 0x0066FF},
                  {text: "零件背包=200", command: "bag_parts_200", color: 0x006600}
               ];
            case "系统":
               return [
                  {text: "帧数30", command: "system_fps_30", color: 0x006600},
                  {text: "帧数60", command: "system_fps_60", color: 0x008800},
                  {text: "帧数120", command: "system_fps_120", color: 0x00AA00},
                  {text: "新的一天", command: "system_newday", color: 0x0066FF},
                  {text: "本地时间", command: "system_localtime", color: 0x006600},
                  {text: "缩放50%", command: "ui_scale_50", color: 0xFF6600},
                  {text: "恢复缩放", command: "ui_scale_100", color: 0x00AA00}
               ];
            case "存档":
               return [
                  {text: "初始化存档", command: "save_init", color: 0xFF9900},
                  {text: "复制Json存档", command: "save_copyjson", color: 0x006600},
                  {text: "复制Xml存档", command: "save_copyxml", color: 0x008800},
                  {text: "修复存档数据", command: "save_repair", color: 0x00AA00},
                  {text: "查询异常原因", command: "save_checkerror", color: 0x0066FF},
                  {text: "自动存档30s", command: "save_auto_30", color: 0x006600},
                  {text: "自动存档60s", command: "save_auto_60", color: 0x008800},
                  {text: "自动存档300s", command: "save_auto_300", color: 0x00AA00}
               ];
            case "任务":
               return [
                  {text: "解锁主线任务", command: "task_unlockmain", color: 0x9900FF},
                  {text: "🎯 完成全部任务", command: "task_completeall", color: 0xFF1493},
                  {text: "解锁任务系统", command: "task_unlocksystem", color: 0x0066FF},
                  {text: "重置任务数据", command: "task_reset", color: 0xFF6600},
                  {text: "设置秘境次数0", command: "wilder_setnum_0", color: 0x008800}
               ];
            case "技能":
               return [
                  {text: "删除当前技能", command: "skill_clear", color: 0xFF6600}
               ];
            case "宠物":
               return [
                  {text: "🔍 调试宠物系统", command: "pet_debug", color: 0xFF3366},
                  {text: "获取所有宠物", command: "pet_getall", color: 0xFF0099},
                  {text: "安全添加宠物", command: "pet_getsafe", color: 0x00FF99},
                  {text: "简化添加宠物", command: "pet_getfew", color: 0x66FF99},
                  {text: "扩展宠物格子", command: "pet_expandbag", color: 0xFF6600},
                  {text: "自定义飞船经验", command: "pet_craftexp_custom", color: 0x008800, needInput: true},
                  {text: "自定义飞船等级", command: "pet_craftlv_custom", color: 0x0066FF, needInput: true},
                  {text: "自定义宠物等级", command: "pet_setlv_custom", color: 0x008800, needInput: true},
                  {text: "测试单个宠物", command: "pet_testsingle", color: 0xFF3366}
               ];
            case "竞技":
               return [
                  {text: "竞技场次数+10", command: "arena_num_10", color: 0xFF6699},
                  {text: "竞技场次数+99", command: "arena_num_99", color: 0x006600},
                  {text: "竞技场分数+1000", command: "arena_score_1000", color: 0x008800},
                  {text: "竞技场分数+9999", command: "arena_score_9999", color: 0x00AA00}
               ];
            case "军团":
               return [
                  {text: "军衔等级1", command: "union_rank_1", color: 0x66FF99},
                  {text: "军衔等级5", command: "union_rank_5", color: 0x006600},
                  {text: "军衔等级10", command: "union_rank_10", color: 0x008800},
                  {text: "军衔等级20", command: "union_rank_20", color: 0x00AA00},
                  {text: "军队等级+1", command: "union_army_1", color: 0x006600},
                  {text: "军队等级+5", command: "union_army_5", color: 0x008800},
                  {text: "军队等级+10", command: "union_army_10", color: 0x00AA00},
                  {text: "设置贡献值", command: "union_contribution", color: 0x0066FF}
               ];
            case "成就":
               return [
                  {text: "🏆 解锁全部成就", command: "achieve_unlockall", color: 0xFF6B9D},
                  {text: "🔄 强制完成所有成就", command: "achieve_forceall", color: 0xFF1493},
                  {text: "杀敌数+1000", command: "achieve_kill_1000", color: 0x006600},
                  {text: "杀敌数+10000", command: "achieve_kill_10000", color: 0x008800},
                  {text: "不掉橙装+10", command: "achieve_noorange_10", color: 0x00AA00},
                  {text: "擒王等级+10", command: "achieve_king_10", color: 0x0066FF},
                  {text: "全是银币+10", command: "achieve_coin_10", color: 0x006600},
                  {text: "全是橙装+5", command: "achieve_orange_5", color: 0x008800}
               ];

            case "时间":
               return [
                  {text: "自定义双倍材料时间", command: "time_doublematerial_custom", color: 0x006600, needInput: true},
                  {text: "自定义双倍经验时间", command: "time_doubleexp_custom", color: 0x008800, needInput: true},
                  {text: "自定义双倍武器时间", command: "time_doublearms_custom", color: 0x00AA00, needInput: true},
                  {text: "自定义双倍装备时间", command: "time_doubleequip_custom", color: 0x0066FF, needInput: true},
                  {text: "清除所有双倍时间", command: "time_clearall", color: 0xFF6600}
               ];
            case "自定义":
               return [
                  {text: "🎴 自定义魂卡属性", command: "custom_card", color: 0xFF1493},
                  {text: "⚔️ 自定义装备属性", command: "custom_equip", color: 0xFF6B9D},
                  {text: "🔫 自定义武器属性", command: "custom_weapon", color: 0xFF4500},
                  {text: "🚗 自定义载具属性", command: "custom_vehicle", color: 0x32CD32},
                  {text: "✨ 自定义技能属性", command: "custom_skill", color: 0x9370DB},
                  {text: "🎁 获取自定义魂卡", command: "custom_getcard", color: 0xFF69B4},
                  {text: "📦 获取自定义装备", command: "custom_getequip", color: 0x87CEEB},
                  {text: "🎯 获取自定义武器", command: "custom_getweapon", color: 0xFFA500},
                  {text: "🚀 获取自定义载具", command: "custom_getvehicle", color: 0x98FB98},
                  {text: "⭐ 获取自定义技能", command: "custom_getskill", color: 0xDDA0DD}
               ];
            case "其他":
               return [
                  {text: "🎯自定义添加系统", command: "custom_addsystem", color: 0xFF0066},
                  {text: "添加妞", command: "more_addgirl", color: 0x9999FF},
                  {text: "设置黄金1000", command: "pay_gold_1000", color: 0x006600},
                  {text: "设置黄金9999", command: "pay_gold_9999", color: 0x008800},
                  {text: "充值100", command: "pay_recharge_100", color: 0x00AA00},
                  {text: "充值1000", command: "pay_recharge_1000", color: 0x0066FF},
                  {text: "显示充值统计", command: "pay_showcount", color: 0xFF6600},
                  {text: "时间加速2倍", command: "time_speed_2", color: 0x006600},
                  {text: "导出所有ID", command: "export_allids", color: 0xFF9900},
                  {text: "自定义职务等级", command: "other_postlv_custom", color: 0x0066FF, needInput: true},
                  {text: "自定义职务经验", command: "other_postexp_custom", color: 0x006600, needInput: true},
                  {text: "自定义粽子数量", command: "other_zongzi_custom", color: 0x008800, needInput: true},
                  {text: "自定义总厨艺值", command: "other_cookskill_custom", color: 0x00AA00, needInput: true},
                  {text: "清除尸宠背包", command: "other_clearpets", color: 0xFF6600},
                  {text: "设置存档异常", command: "other_setcheat", color: 0xFF3300},
                  {text: "清除溢出物品", command: "other_clearoverflow", color: 0xFF9900}
               ];
            default:
               return [];
         }
      }
      
      private function onFunctionClick(e:MouseEvent):void
      {
         var btn:Sprite = e.currentTarget as Sprite;
         var command:String = btn.name;

         // 检查是否需要输入
         if(needsInput(command))
         {
            showInputBox(command);
         }
         else
         {
            executeGMFunction(command);
         }
      }

      private function needsInput(command:String):Boolean
      {
         // 需要输入的命令列表
         var inputCommands:Array = [
            "player_title", "player_nickname", "player_level_custom", "player_exp_custom",
            "player_coin_custom", "player_score_custom", "equip_custom", "item_custom",
            "pet_craftexp_custom", "pet_craftlv_custom", "pet_setlv_custom",
            "other_postlv_custom", "other_postexp_custom", "other_zongzi_custom", "other_cookskill_custom",
            "time_doublematerial_custom", "time_doubleexp_custom", "time_doublearms_custom", "time_doubleequip_custom"
         ];

         return inputCommands.indexOf(command) != -1;
      }

      private function showInputBox(command:String):void
      {
         currentInputCommand = command;
         inputContainer.visible = true;
         inputBox.text = "";

         // 设置提示文本
         var hints:Object = {
            "player_title": "请输入称号名称:",
            "player_nickname": "请输入新昵称:",
            "player_level_custom": "请输入等级数值:",
            "player_exp_custom": "请输入经验数值:",
            "player_coin_custom": "请输入银币数值:",
            "player_score_custom": "请输入积分数值:",
            "equip_custom": "请输入装备名称:",
            "item_custom": "请输入物品名称:",
            "pet_craftexp_custom": "请输入飞船经验数值:",
            "pet_craftlv_custom": "请输入飞船等级数值:",
            "pet_setlv_custom": "请输入宠物等级数值:",
            "other_postlv_custom": "请输入职务等级数值:",
            "other_postexp_custom": "请输入职务经验数值:",
            "other_zongzi_custom": "请输入粽子数量:",
            "other_cookskill_custom": "请输入总厨艺值:",
            "time_doublematerial_custom": "请输入双倍材料时间(分钟):",
            "time_doubleexp_custom": "请输入双倍经验时间(分钟):",
            "time_doublearms_custom": "请输入双倍武器时间(分钟):",
            "time_doubleequip_custom": "请输入双倍装备时间(分钟):"
         };

         var hint:String = hints[command] || "请输入数值:";
         inputLabel.text = hint;

         addOutput("请在输入框中输入相应的值，然后点击确认");
      }

      private function onInputConfirm(e:MouseEvent = null):void
      {
         var inputValue:String = inputBox.text.replace(/^\s+|\s+$/g, ""); // 去除首尾空格

         if(inputValue == "")
         {
            addOutput("❌ 输入不能为空");
            return;
         }

         inputContainer.visible = false;

         // 执行带输入值的命令
         executeGMFunctionWithInput(currentInputCommand, inputValue);
      }

      private function executeGMFunctionWithInput(command:String, inputValue:String):void
      {
         try
         {
            // 强制启用GM功能
            Gaming.testCtrl.enabled = true;
            Gaming.testCtrl.cheating.enabled = true;

            var result:String = "";
            var numValue:int = parseInt(inputValue);

            switch(command)
            {
               case "player_title":
                  // 尝试通过称号名称添加称号
                  try
                  {
                     // 常见称号映射表
                     var titleMap:Object = {
                        "十年相伴": "anniver10",
                        "九周年快乐": "anniver9",
                        "八周年快乐": "anniver8",
                        "成就之皇": "achieveKing",
                        "成就之神": "achieveGod",
                        "武器制造大师": "armsSkinCreator",
                        "群组达人": "bbs23",
                        "十二生肖": "zodiac12",
                        "霞光领主": "battle4",
                        "霞光天军": "battle3",
                        "霞光雄狮": "battle2",
                        "霞光劲旅": "battle1",
                        "愚人欢乐365": "joyousFool",
                        "佣兵之王": "gameKing",
                        "佣兵精英": "gameSuper"
                     };

                     var headCode:String = titleMap[inputValue];
                     if(headCode)
                     {
                        Gaming.PG.da.head.addHead(headCode, Gaming.api.save.getNowServerDate().getStr());
                        result = "添加称号: " + inputValue;
                     }
                     else
                     {
                        // 检查是否是预定义的称号
                        var headDefine:* = Gaming.defineGroup.head.getCnDefine(inputValue);
                        if(headDefine)
                        {
                           Gaming.PG.da.head.addHead(headDefine.name, Gaming.api.save.getNowServerDate().getStr());
                           result = "添加称号: " + inputValue;
                        }
                        else
                        {
                           // 尝试直接通过代码添加
                           Gaming.PG.da.head.addHead(inputValue, Gaming.api.save.getNowServerDate().getStr());
                           result = "添加称号代码: " + inputValue;
                        }
                     }
                  }
                  catch(e:Error)
                  {
                     result = "称号添加失败: " + inputValue + " (可能不存在此称号)\n常见称号: 十年相伴, 成就之皇, 佣兵之王, 霞光领主等";
                  }
                  break;
               case "equip_addbyname":
                  try
                  {
                     // 尝试通过中文名称查找装备
                     var equipDefine:* = Gaming.defineGroup.getAllEquipDefine(inputValue);
                     if(!equipDefine)
                     {
                        // 如果直接查找失败，尝试通过中文名称查找
                        equipDefine = Gaming.defineGroup.equip.getCnDefine(inputValue);
                     }

                     if(equipDefine)
                     {
                        var equipSave:* = GiftAddit.getEquipSaveByName(equipDefine.name, Gaming.PG.da.level, 1);
                        if(equipSave)
                        {
                           Gaming.PG.da.equipBag.addSave(equipSave);
                           result = "添加装备: " + inputValue + " (" + equipDefine.name + ")";
                        }
                        else
                        {
                           result = "装备创建失败: " + inputValue;
                        }
                     }
                     else
                     {
                        result = "装备不存在: " + inputValue;
                     }
                  }
                  catch(e:Error)
                  {
                     result = "装备添加失败: " + e.message;
                  }
                  break;
               case "things_addbyname":
                  try
                  {
                     var thingsDefine:* = Gaming.defineGroup.things.getCnDefine(inputValue);
                     if(thingsDefine)
                     {
                        Gaming.PG.da.thingsBag.addDataByName(thingsDefine.name, 1);
                        result = "添加物品: " + inputValue + " (" + thingsDefine.name + ")";
                     }
                     else
                     {
                        // 尝试直接通过名称添加
                        var thingsDefine2:* = Gaming.defineGroup.things.getDefine(inputValue);
                        if(thingsDefine2)
                        {
                           Gaming.PG.da.thingsBag.addDataByName(inputValue, 1);
                           result = "添加物品: " + inputValue;
                        }
                        else
                        {
                           result = "物品不存在: " + inputValue;
                        }
                     }
                  }
                  catch(e:Error)
                  {
                     result = "物品添加失败: " + e.message;
                  }
                  break;
               case "player_nickname":
                  Gaming.PG.save.base.playerName = inputValue;
                  result = "设置昵称为: " + inputValue;
                  break;
               case "player_level_custom":
                  Gaming.testCtrl.cheating.doOrder("player", "setHeroLv", "", numValue);
                  result = "设置等级为: " + numValue;
                  break;
               case "player_exp_custom":
                  Gaming.testCtrl.cheating.doOrder("player", "addHeroExp", "", numValue);
                  result = "增加经验: " + numValue;
                  break;
               case "player_coin_custom":
                  Gaming.testCtrl.cheating.doOrder("player", "addCoin", "", numValue);
                  result = "增加银币: " + numValue;
                  break;
               case "player_score_custom":
                  Gaming.testCtrl.cheating.doOrder("player", "addSore", "", numValue);
                  result = "增加积分: " + numValue;
                  break;
               case "equip_custom":
                  try
                  {
                     var addResult:Boolean = this.smartAddItem(inputValue, 1);
                     if(addResult)
                     {
                        result = "成功添加装备: " + inputValue;
                     }
                     else
                     {
                        result = "未找到装备: " + inputValue + "，请检查装备名称";
                     }
                  }
                  catch(e:Error)
                  {
                     result = "添加装备失败: " + e.message;
                  }
                  break;
               case "item_custom":
                  try
                  {
                     var addResult2:Boolean = this.smartAddItem(inputValue, 1);
                     if(addResult2)
                     {
                        result = "成功添加物品: " + inputValue;
                     }
                     else
                     {
                        result = "未找到物品: " + inputValue + "，请检查物品名称";
                     }
                  }
                  catch(e:Error)
                  {
                     result = "添加物品失败: " + e.message;
                  }
                  break;
               case "pet_craftexp_custom":
                  Gaming.testCtrl.cheating.doOrder("pet", "addNowCraftExp", "", numValue);
                  result = "设置飞船经验: " + numValue;
                  break;
               case "pet_craftlv_custom":
                  Gaming.testCtrl.cheating.doOrder("pet", "setNowCraftLv", "", numValue);
                  result = "设置飞船等级: " + numValue;
                  break;
               case "pet_setlv_custom":
                  Gaming.testCtrl.cheating.doOrder("pet", "setPetLv", "", numValue);
                  result = "设置宠物等级: " + numValue;
                  break;
               case "other_postlv_custom":
                  try
                  {
                     Gaming.PG.da.post.save.postLv = numValue;
                     result = "设置职务等级为: " + numValue + " 级";
                  }
                  catch(e:Error)
                  {
                     result = "设置职务等级失败: " + e.message;
                  }
                  break;
               case "other_postexp_custom":
                  try
                  {
                     Gaming.PG.da.post.save.postExp = numValue;
                     result = "设置职务经验为: " + numValue;
                  }
                  catch(e:Error)
                  {
                     result = "设置职务经验失败: " + e.message;
                  }
                  break;
               case "other_zongzi_custom":
                  try
                  {
                     Gaming.PG.da.main.save.zongzi25 = numValue;
                     Gaming.uiGroup.mainUI.show();
                     result = "设置粽子数量为: " + numValue;
                  }
                  catch(e:Error)
                  {
                     result = "设置粽子数量失败: " + e.message;
                  }
                  break;
               case "other_cookskill_custom":
                  try
                  {
                     Gaming.PG.da.food.save.profiAll = numValue;
                     Gaming.PG.da.food.addProfi(0);
                     result = "设置总厨艺值为: " + numValue;
                  }
                  catch(e:Error)
                  {
                     result = "设置总厨艺值失败: " + e.message;
                  }
                  break;
               case "time_doublematerial_custom":
                  try
                  {
                     Gaming.PG.SAVE.time.doubleMaterialsDropTime = numValue;
                     result = "设置双倍材料时间为: " + numValue + " 分钟";
                  }
                  catch(e:Error)
                  {
                     result = "设置双倍材料时间失败: " + e.message;
                  }
                  break;
               case "time_doubleexp_custom":
                  try
                  {
                     Gaming.PG.SAVE.time.doubleExpTime = numValue;
                     result = "设置双倍经验时间为: " + numValue + " 分钟";
                  }
                  catch(e:Error)
                  {
                     result = "设置双倍经验时间失败: " + e.message;
                  }
                  break;
               case "time_doublearms_custom":
                  try
                  {
                     Gaming.PG.SAVE.time.doubleArmsDropTime = numValue;
                     result = "设置双倍武器时间为: " + numValue + " 分钟";
                  }
                  catch(e:Error)
                  {
                     result = "设置双倍武器时间失败: " + e.message;
                  }
                  break;
               case "time_doubleequip_custom":
                  try
                  {
                     Gaming.PG.SAVE.time.doubleEquipDropTime = numValue;
                     result = "设置双倍装备时间为: " + numValue + " 分钟";
                  }
                  catch(e:Error)
                  {
                     result = "设置双倍装备时间失败: " + e.message;
                  }
                  break;
               case "level_bosslife_custom":
                  var boss:* = Gaming.BG.filter.getEnemyBoss();
                  if(boss)
                  {
                     if(numValue <= 0)
                     {
                        Gaming.TG.hurt.toDie(boss);
                        result = "Boss已死亡";
                     }
                     else
                     {
                        boss.getData().setLifePer(numValue / 100);
                        result = "设置Boss血量: " + numValue + "%";
                     }
                  }
                  else
                  {
                     result = "当前关卡没有Boss";
                  }
                  break;
               case "arena_num_custom":
                  Gaming.PG.save.arena.todayNum = numValue;
                  result = "设置竞技场次数: " + numValue;
                  break;
               case "arena_score_custom":
                  Gaming.PG.save.arena.score = numValue;
                  result = "设置竞技场分数: " + numValue;
                  break;
               case "space_level_custom":
                  try
                  {
                     Gaming.testCtrl.cheating.doOrder("pet", "setNowCraftLv", "", numValue);
                     result = "设置飞船等级: " + numValue;
                  }
                  catch(e:Error)
                  {
                     result = "设置飞船等级失败: " + e.message;
                  }
                  break;
               case "union_rank_custom":
                  var militaryDef:* = Gaming.defineGroup.union.military.getDefine(numValue + "");
                  if(militaryDef)
                  {
                     Gaming.testCtrl.cheating.tempContribution = militaryDef.totalMust;
                     result = "设置军衔等级: " + numValue + " (" + militaryDef.cnName + ")";
                  }
                  else
                  {
                     result = "军衔等级 " + numValue + " 不存在";
                  }
                  break;
               case "achieve_kill_custom":
                  Gaming.PG.save.count.killNum = numValue;
                  result = "设置杀敌数: " + numValue;
                  break;
               case "time_speed_custom":
                  CountCtrl.onlineTimePer = numValue;
                  result = "设置时间加速倍数: " + numValue;
                  break;
               case "tower_complete_all":
                  executeGMFunction("tower_all");
                  return;
               default:
                  result = "未知的输入命令: " + command;
                  break;
            }

            addOutput("✅ " + result);
         }
         catch(error:Error)
         {
            addOutput("❌ 执行失败: " + error.message);
         }
      }

      private function executeGMFunction(command:String):void
      {
         try
         {
            // 强制启用GM功能
            Gaming.testCtrl.enabled = true;
            Gaming.testCtrl.cheating.enabled = true;

            var result:String = "";
            var parts:Array = command.split("_");
            var category:String = parts[0];
            var action:String = parts[1];
            var value:String = parts.length > 2 ? parts[2] : "0";
            var v:int = parseInt(value);

            // 使用doOrder方法来执行GM命令
            switch(category)
            {
               case "player":
                  switch(action)
                  {
                     case "level":
                        Gaming.testCtrl.cheating.doOrder("player", "setHeroLv", "", v);
                        result = "设置等级为: " + v;
                        break;
                     case "addlevel":
                        var currentLevel:int = Gaming.PG.DATA.base.save.level;
                        var newLevel:int = currentLevel + v;
                        Gaming.testCtrl.cheating.doOrder("player", "setHeroLv", "", newLevel);
                        result = "等级从 " + currentLevel + " 增加到 " + newLevel;
                        break;
                     case "exp":
                        Gaming.testCtrl.cheating.doOrder("player", "addHeroExp", "", v);
                        result = "增加经验: " + v;
                        break;
                     case "coin":
                        Gaming.testCtrl.cheating.doOrder("player", "addCoin", "", v);
                        result = "增加银币: " + v;
                        break;
                     case "score":
                        Gaming.testCtrl.cheating.doOrder("player", "addSore", "", v);
                        result = "增加积分: " + v;
                        break;
                     case "nocheat":
                        Gaming.testCtrl.cheating.doOrder("player", "noZuobi", "", 0);
                        result = "解除作弊状态";
                        break;
                     case "newpartner":
                        Gaming.testCtrl.cheating.doOrder("player", "openNewPartner", "", 0);
                        result = "开启新队友";
                        break;
                     case "fullhp":
                        Gaming.testCtrl.cheating.doOrder("level", "fullHp", "", 0);
                        result = "满血";
                        break;
                     case "fullmp":
                        Gaming.testCtrl.cheating.doOrder("level", "fullMp", "", 0);
                        result = "满蓝";
                        break;
                     case "godmode":
                        Gaming.testCtrl.cheating.doOrder("level", "godMode", "", 0);
                        result = "切换无敌模式";
                        break;
                     case "reset":
                        Gaming.testCtrl.cheating.doOrder("save", "initPlayerSave", "", 0);
                        result = "重置玩家数据";
                        break;
                     case "peaklv":
                        Gaming.PG.da.peak.setLevel(v);
                        result = "设置巅峰等级: " + v;
                        break;
                     case "addpeaklv":
                        var currentPeakLevel:int = Gaming.PG.da.peak.level;
                        var newPeakLevel:int = currentPeakLevel + v;
                        Gaming.PG.da.peak.setLevel(newPeakLevel);
                        result = "巅峰等级从 " + currentPeakLevel + " 增加到 " + newPeakLevel;
                        break;
                     case "peakexp":
                        Gaming.PG.da.peak.addExp(v);
                        result = "增加巅峰经验: " + v;
                        break;
                     case "title":
                        result = "称号功能需要输入具体称号名";
                        break;
                     case "allheads":
                        try
                        {
                           var successCount:int = 0;
                           var currentTime:String = Gaming.api.save.getNowServerDate().getStr();

                           // 方法1: 尝试从游戏系统中获取所有称号
                           try
                           {
                              var headDefineGroup:* = Gaming.defineGroup.head;
                              if(headDefineGroup && headDefineGroup.arr)
                              {
                                 var headDefineArr:Array = headDefineGroup.arr;
                                 for each(var headDefine:* in headDefineArr)
                                 {
                                    if(headDefine && headDefine.name)
                                    {
                                       try
                                       {
                                          Gaming.PG.da.head.addHead(headDefine.name, currentTime);
                                          successCount++;
                                       }
                                       catch(addError:Error)
                                       {
                                          continue;
                                       }
                                    }
                                 }
                              }
                           }
                           catch(systemError:Error)
                           {
                              // 如果系统方法失败，使用预定义列表
                              var allTitles:Array = [
                                 // 周年纪念称号
                                 "anniver10", "anniver9", "anniver8", "anniver7", "anniver6", "anniver5",
                                 // 成就相关称号
                                 "achieveKing", "achieveGod", "achieveSuper", "achieveHigh", "achieveNormal",
                                 // 特殊称号
                                 "armsSkinCreator", "bbs23", "zodiac12", "joyousFool",
                                 // 霞光系列称号
                                 "battle4", "battle3", "battle2", "battle1",
                                 // 佣兵系列称号
                                 "gameKing", "gameSuper", "gameHigh", "gameNormal",
                                 // 其他称号
                                 "unionKing", "unionSuper", "arenaKing", "arenaSuper",
                                 "towerKing", "towerSuper", "petKing", "petSuper",
                                 "equipKing", "equipSuper", "levelKing", "levelSuper",
                                 "coinKing", "coinSuper", "timeKing", "timeSuper",
                                 "killKing", "killSuper", "bossKing", "bossSuper",
                                 "mapKing", "mapSuper", "taskKing", "taskSuper",
                                 "loveKing", "loveSuper", "vipKing", "vipSuper",
                                 "payKing", "paySuper", "loginKing", "loginSuper"
                              ];

                              for each(var titleCode:String in allTitles)
                              {
                                 try
                                 {
                                    Gaming.PG.da.head.addHead(titleCode, currentTime);
                                    successCount++;
                                 }
                                 catch(titleError:Error)
                                 {
                                    continue;
                                 }
                              }
                           }

                           result = "成功添加 " + successCount + " 个称号！";
                        }
                        catch(e:Error)
                        {
                           result = "获取全部称号失败: " + e.message;
                        }
                        break;
                     case "nickname":
                        result = "昵称功能需要输入具体昵称";
                        break;
                  }
                  break;
               case "equip":
                  switch(action)
                  {
                     case "custom":
                        try
                        {
                           // 尝试智能添加装备
                           var addResult:Boolean = this.smartAddItem(inputValue, 1);
                           if(addResult)
                           {
                              result = "成功添加装备: " + inputValue;
                           }
                           else
                           {
                              result = "未找到装备: " + inputValue + "，请检查装备名称";
                           }
                        }
                        catch(e:Error)
                        {
                           result = "添加装备失败: " + e.message;
                        }
                        break;
                     case "bestsuits":
                        try
                        {
                           // 扩展装备格子
                           Gaming.PG.da.equipBag.saveGroup.unlockTo(200);
                           // 清空装备背包
                           Gaming.PG.da.equipBag.clearData();
                           // 添加最强套装
                           Gaming.testCtrl.arms.addAllSuit();
                           result = "已清空装备背包并添加所有最强套装！";
                        }
                        catch(e:Error)
                        {
                           result = "添加最强套装失败: " + e.message;
                        }
                        break;
                     case "blackarms":
                        try
                        {
                           // 扩展武器格子
                           Gaming.PG.da.armsBag.saveGroup.unlockTo(200);
                           // 添加所有黑色武器
                           Gaming.testCtrl.cheating.doOrder("equip", "addBlackArms", "", 0);
                           result = "添加所有黑色武器成功！";
                        }
                        catch(e:Error)
                        {
                           result = "添加黑色武器失败: " + e.message;
                        }
                        break;
                     case "chip":
                        Gaming.testCtrl.cheating.doOrder("equip", "addEquipChip", "", v);
                        result = "添加装备碎片: " + v;
                        break;
                     case "armschip":
                        Gaming.testCtrl.cheating.doOrder("equip", "addArmsChip", "", v);
                        result = "添加武器碎片: " + v;
                        break;
                     case "fashion":
                        Gaming.testCtrl.cheating.doOrder("equip", "addAllFashion", "", 0);
                        result = "添加所有时装";
                        break;
                     case "device":
                        Gaming.testCtrl.cheating.doOrder("equip", "addAllDevice", "", v);
                        result = "添加所有装置: " + v;
                        break;
                     case "weapon":
                        Gaming.testCtrl.cheating.doOrder("equip", "addAllWeapon", "", v);
                        result = "添加所有兵器: " + v;
                        break;
                     case "upgradearms":
                        Gaming.testCtrl.cheating.doOrder("equip", "testBlackArms", "", 0);
                        result = "升级背包所有武器到99级";
                        break;
                     case "clear":
                        Gaming.PG.da.equipBag.clearData();
                        result = "装备背包已清空！";
                        break;
                     case "expandbag":
                        Gaming.PG.da.equipBag.saveGroup.unlockTo(200);
                        result = "装备格子已扩展到200个！";
                        break;
                  }
                  break;
               case "arms":
                  switch(action)
                  {
                     case "clear":
                        Gaming.PG.da.armsBag.clearData();
                        result = "武器背包已清空！";
                        break;
                     case "expandbag":
                        Gaming.PG.da.armsBag.saveGroup.unlockTo(200);
                        result = "武器格子已扩展到200个！";
                        break;
                  }
                  break;
               case "level":
                  switch(action)
                  {
                     case "win":
                        Gaming.LG.levelWin("r_over");
                        result = "胜利当前关卡";
                        break;
                     case "restart":
                        Gaming.testCtrl.cheating.doOrder("level", "restart", "", 0);
                        result = "重启当前关卡";
                        break;
                     case "testenemy":
                        Gaming.testCtrl.cheating.doOrder("level", "testEnemy", "", 0);
                        result = "切换测试敌人状态";
                        break;
                     case "bosslife":
                        var boss:* = Gaming.BG.filter.getEnemyBoss();
                        if(boss)
                        {
                           if(v <= 0)
                           {
                              Gaming.TG.hurt.toDie(boss);
                              result = "Boss已死亡";
                           }
                           else
                           {
                              boss.getData().setLifePer(v / 100);
                              result = "设置Boss血量: " + v + "%";
                           }
                        }
                        else
                        {
                           result = "当前关卡没有Boss";
                        }
                        break;
                     case "killboss":
                        var boss2:* = Gaming.BG.filter.getEnemyBoss();
                        if(boss2)
                        {
                           Gaming.TG.hurt.toDie(boss2);
                           result = "Boss已被杀死";
                        }
                        else
                        {
                           result = "当前关卡没有Boss";
                        }
                        break;
                  }
                  break;
               case "map":
                  switch(action)
                  {
                     case "unlockall":
                        Gaming.PG.da.worldMap.saveGroup.unlockAll();
                        Gaming.uiGroup.mainUI.show();
                        result = "解锁所有地图";
                        break;
                     case "winall":
                        Gaming.PG.da.worldMap.saveGroup.unlockAll();
                        Gaming.PG.da.worldMap.saveGroup.winAll();
                        Gaming.uiGroup.mainUI.show();
                        result = "通关所有地图";
                        break;
                     case "unlockwilder":
                        Gaming.PG.da.wilder.unlockAllWider();
                        result = "解锁所有秘境";
                        break;
                     case "wilderkey":
                        Gaming.PG.da.wilder.saveGroup.keyNum = v;
                        result = "设置秘境钥匙: " + v;
                        break;
                     case "sweep":
                        Gaming.PG.da.main.save.daySweeping = v;
                        result = "设置扫荡次数: " + v;
                        break;
                  }
                  break;
               case "item":
                  switch(action)
                  {
                     case "custom":
                        try
                        {
                           // 尝试智能添加物品
                           var addResult:Boolean = this.smartAddItem(inputValue, 1);
                           if(addResult)
                           {
                              result = "成功添加物品: " + inputValue;
                           }
                           else
                           {
                              result = "未找到物品: " + inputValue + "，请检查物品名称";
                           }
                        }
                        catch(e:Error)
                        {
                           result = "添加物品失败: " + e.message;
                        }
                        break;
                     case "all":
                        Gaming.testCtrl.cheating.doOrder("things", "addAllThings", "", v);
                        result = "添加所有物品: " + v;
                        break;
                     case "key":
                        Gaming.testCtrl.cheating.doOrder("things", "addAllKey", "", v);
                        result = "添加钥匙: " + v;
                        break;
                  }
                  break;
               case "things":
                  switch(action)
                  {
                     case "clear":
                        Gaming.PG.da.thingsBag.clearData();
                        result = "物品背包已清空！";
                        break;
                  }
                  break;
               case "gene":
                  switch(action)
                  {
                     case "clear":
                        Gaming.PG.da.geneBag.clearData();
                        result = "基因背包已清空！";
                        break;
                  }
                  break;
               case "parts":
                  switch(action)
                  {
                     case "clear":
                        Gaming.PG.da.partsBag.clearData();
                        result = "零件背包已清空！";
                        break;
                  }
                  break;
               case "skill":
                  switch(action)
                  {
                     case "clear":
                        Gaming.PG.da.skillBag.clearData();
                        result = "技能背包已清空！";
                        break;
                  }
                  break;
               case "bag":
                  switch(action)
                  {
                     case "arms":
                        Gaming.PG.da.armsBag.saveGroup.unlockTo(v);
                        result = "武器背包扩展到: " + v + " 个格子";
                        break;
                     case "equip":
                        Gaming.PG.da.equipBag.saveGroup.unlockTo(v);
                        result = "装备背包扩展到: " + v + " 个格子";
                        break;
                     case "things":
                        Gaming.PG.da.thingsBag.saveGroup.unlockTo(v);
                        result = "物品背包扩展到: " + v + " 个格子";
                        break;
                     case "gene":
                        Gaming.PG.da.geneBag.saveGroup.unlockTo(v);
                        result = "基因背包扩展到: " + v + " 个格子";
                        break;
                     case "parts":
                        Gaming.PG.da.partsBag.saveGroup.unlockTo(v);
                        result = "零件背包扩展到: " + v + " 个格子";
                        break;
                     case "skill":
                        Gaming.PG.da.skillBag.saveGroup.unlockTo(v);
                        result = "技能背包扩展到: " + v + " 个格子";
                        break;
                  }
                  break;
               case "food":
                  switch(action)
                  {
                     case "raw":
                        Gaming.PG.da.food.addRawAll(v);
                        result = "添加所有食材: " + v;
                        break;
                  }
                  break;
               case "system":
                  switch(action)
                  {
                     case "fps":
                        Gaming.testCtrl.cheating.doOrder("system", "setFrame", "", v);
                        result = "设置帧数: " + v;
                        break;
                     case "newday":
                        Gaming.testCtrl.cheating.doOrder("system", "newDay", "", 0);
                        result = "触发新的一天";
                        break;
                     case "localtime":
                        Gaming.testCtrl.cheating.doOrder("system", "setToLocalTime", "", 0);
                        result = "切换本地时间";
                        break;
                  }
                  break;
               case "ui":
                  switch(action)
                  {
                     case "scale":
                        Gaming.testCtrl.cheating.doOrder("ui", "scaleScene", "", v);
                        result = "设置缩放: " + v + "%";
                        break;
                  }
                  break;
               case "save":
                  switch(action)
                  {
                     case "init":
                        Gaming.PG.initSave();
                        Gaming.uiGroup.mainUI.show();
                        result = "初始化存档成功";
                        break;
                     case "copyjson":
                        System.setClipboard(JSON2.encode(ClassProperty.copyObj(Gaming.PG.save)));
                        result = "已复制Json存档到剪贴板";
                        break;
                     case "copyxml":
                        System.setClipboard(ObjectToXml.decode4399(ClassProperty.copyObj(Gaming.PG.save)));
                        result = "已复制Xml存档到剪贴板";
                        break;
                     case "repair":
                        PlayerDataSupple.dealSummer(Gaming.PG.da);
                        result = "修复存档数据成功";
                        break;
                     case "checkerror":
                        result = "异常原因: " + Gaming.PG.save.main.zuobiReason;
                        break;
                     case "auto":
                        Gaming.testCtrl.cheating.doOrder("save", "autoSaveTime", "", v);
                        result = "设置自动存档间隔: " + v + "秒";
                        break;
                  }
                  break;
               case "task":
                  switch(action)
                  {
                     case "unlockmain":
                        try
                        {
                           Gaming.testCtrl.cheating.doOrder("task", "unlockMainTask", "", 0);
                           result = "解锁所有主线任务";
                        }
                        catch(e:Error)
                        {
                           result = "解锁主线任务失败: " + e.message;
                        }
                        break;
                     case "unlocksystem":
                        try
                        {
                           Gaming.testCtrl.cheating.doOrder("task", "unlockTask", "", 0);
                           result = "解锁任务系统";
                        }
                        catch(e:Error)
                        {
                           result = "解锁任务系统失败: " + e.message;
                        }
                        break;
                     case "completeall":
                        try
                        {
                           // 强制启用GM功能
                           Gaming.testCtrl.enabled = true;
                           Gaming.testCtrl.cheating.enabled = true;

                           var totalUnlocked:int = 0;
                           var totalCompleted:int = 0;
                           var totalAccepted:int = 0;

                           // 方法1: 尝试使用GM命令
                           try
                           {
                              Gaming.testCtrl.cheating.doOrder("task", "unlockMainTask", "", 0);
                              result = "✅ 通过GM命令完成所有任务";
                           }
                           catch(gmError:Error)
                           {
                              // 方法2: 手动处理任务
                              try
                              {
                                 // 解锁所有类型的任务
                                 var taskTypes:Array = ["main", "day", "treasure", "extra", "king", "memory", "deputy", "spread", "week"];

                                 for each(var taskType:String in taskTypes)
                                 {
                                    try
                                    {
                                       Gaming.PG.da.task.unlockAllByType(taskType);
                                       totalUnlocked++;
                                    }
                                    catch(unlockError:Error)
                                    {
                                       continue;
                                    }
                                 }

                                 // 完成所有已解锁的任务
                                 var taskObj:Object = Gaming.PG.da.task.dataObj;
                                 var taskData:*;

                                 for(var taskName:String in taskObj)
                                 {
                                    taskData = taskObj[taskName];
                                    if(taskData)
                                    {
                                       try
                                       {
                                          // 如果任务还没接受，先接受
                                          if(taskData.state == "no")
                                          {
                                             taskData.getTask();
                                             totalAccepted++;
                                          }
                                          // 如果任务正在进行中，完成它
                                          if(taskData.state == "ing")
                                          {
                                             taskData.complete();
                                             totalCompleted++;
                                          }
                                       }
                                       catch(taskError:Error)
                                       {
                                          continue;
                                       }
                                    }
                                 }

                                 // 强制刷新任务界面
                                 try
                                 {
                                    if(Gaming.uiGroup.taskUI && Gaming.uiGroup.taskUI.visible)
                                    {
                                       Gaming.uiGroup.taskUI.hide();
                                       TweenLite.delayedCall(0.1, function():void {
                                          Gaming.uiGroup.taskUI.show();
                                       });
                                    }
                                 }
                                 catch(uiError:Error)
                                 {
                                    // UI刷新失败不影响主要功能
                                 }

                                 result = "🎯 解锁 " + totalUnlocked + " 类任务，接受 " + totalAccepted + " 个任务，完成 " + totalCompleted + " 个任务";
                              }
                              catch(manualError:Error)
                              {
                                 result = "❌ 手动完成任务失败: " + manualError.message;
                              }
                           }
                        }
                        catch(e:Error)
                        {
                           result = "❌ 完成任务失败: " + e.message;
                        }
                        break;
                     case "reset":
                        Gaming.PG.da.task.clearData();
                        result = "重置任务数据";
                        break;
                  }
                  break;
               case "skill":
                  switch(action)
                  {
                     case "unlock":
                        Gaming.testCtrl.cheating.doOrder("skill", "unlockSkill", "", 0);
                        result = "解锁技能系统";
                        break;
                     case "profi":
                        Gaming.testCtrl.cheating.doOrder("skill", "addProfi", "", v);
                        result = "增加技能熟练度: " + v;
                        break;
                     case "setprofi":
                        Gaming.testCtrl.cheating.doOrder("skill", "setProfi", "", v);
                        result = "设置技能熟练度: " + v;
                        break;
                     case "dayprofi":
                        Gaming.testCtrl.cheating.doOrder("skill", "addProfi", "", v);
                        result = "增加今日熟练度: " + v;
                        break;
                     case "setdayprofi":
                        Gaming.testCtrl.cheating.doOrder("skill", "setDayProfi", "", v);
                        result = "设置今日熟练度: " + v;
                        break;
                     case "clear":
                        Gaming.testCtrl.cheating.doOrder("skill", "clearNowSkill", "", 0);
                        result = "删除当前技能";
                        break;
                  }
                  break;
               case "achieve":
                  switch(action)
                  {
                     case "complete":
                        Gaming.testCtrl.cheating.doOrder("achieve", "completeAllAchieve", "", 0);
                        result = "完成所有成就";
                        break;
                     case "clear":
                        Gaming.testCtrl.cheating.doOrder("achieve", "reachieve", "", 0);
                        result = "清空当前成就列表";
                        break;
                  }
                  break;
               case "wilder":
                  switch(action)
                  {
                     case "unlock":
                        Gaming.testCtrl.cheating.doOrder("wilder", "unlockAllWider", "", 0);
                        result = "解锁所有秘境";
                        break;
                     case "bossedit":
                        Gaming.testCtrl.cheating.doOrder("wilder", "ulockAllBossEditLevel", "", 0);
                        result = "解锁Boss编辑关卡";
                        break;
                     case "setnum":
                        Gaming.testCtrl.cheating.doOrder("wilder", "setNowWiderNum", "", v);
                        result = "设置当前秘境使用次数: " + v;
                        break;
                  }
                  break;
               case "pet":
                  switch(action)
                  {
                     case "getall":
                        try
                        {
                           // 强制启用GM功能
                           Gaming.testCtrl.enabled = true;
                           Gaming.testCtrl.cheating.enabled = true;

                           // 方法1: 尝试使用GM命令
                           try
                           {
                              Gaming.testCtrl.cheating.doOrder("pet", "clearAllPet", "", 0);
                              Gaming.testCtrl.cheating.doOrder("pet", "addAllPet", "", 0);
                              result = "✅ 通过GM命令获取所有宠物成功";
                           }
                           catch(gmError:Error)
                           {
                              // 方法2: 使用基因定义组获取所有可用宠物
                              try
                              {
                                 // 先扩展宠物格子
                                 Gaming.PG.da.pet.addBagNum(200);

                                 var addedPets:int = 0;
                                 var failedPets:int = 0;
                                 var debugInfo:Array = [];

                                 // 获取所有正常的宠物名称（等级1且非超级宠物）
                                 var normalPetNames:Array = Gaming.defineGroup.gene.getNormalNameArr();
                                 debugInfo.push("找到 " + normalPetNames.length + " 个基础宠物");

                                 // 如果没有找到正常宠物，使用备用列表
                                 if(normalPetNames.length == 0)
                                 {
                                    normalPetNames = [
                                       "BoomSkull", "IronChief", "Lake", "FightWolf", "ZombieWolf",
                                       "ZombieKing", "ZombieCleaver", "IronChiefSecond", "Laer",
                                       "PetLake", "BoomWolf", "ZombieChief"
                                    ];
                                    debugInfo.push("使用备用宠物列表");
                                 }

                                 for each(var petName:String in normalPetNames)
                                 {
                                    try
                                    {
                                       // 检查宠物背包空间
                                       if(Gaming.PG.da.pet.getSpaceNum() <= 0)
                                       {
                                          Gaming.PG.da.pet.addBagNum(20);
                                       }

                                       // 检查基因定义是否存在
                                       var geneDefine:* = Gaming.defineGroup.gene.getDefine(petName);
                                       if(!geneDefine)
                                       {
                                          debugInfo.push("跳过未定义的宠物: " + petName);
                                          failedPets++;
                                          continue;
                                       }

                                       // 创建基因保存数据
                                       var geneSave:* = Gaming.defineGroup.geneCreator.getSave("red", 1, petName, true);
                                       if(geneSave)
                                       {
                                          // 创建基因数据
                                          var geneData:* = Gaming.defineGroup.geneCreator.getTempData(geneSave);
                                          if(geneData)
                                          {
                                             // 添加到宠物背包
                                             var petData:* = Gaming.PG.da.pet.addByGeneData(geneData);
                                             if(petData)
                                             {
                                                addedPets++;
                                                debugInfo.push("成功添加: " + petName);
                                             }
                                             else
                                             {
                                                failedPets++;
                                                debugInfo.push("添加失败: " + petName + " (addByGeneData返回null)");
                                             }
                                          }
                                          else
                                          {
                                             failedPets++;
                                             debugInfo.push("添加失败: " + petName + " (getTempData返回null)");
                                          }
                                       }
                                       else
                                       {
                                          failedPets++;
                                          debugInfo.push("添加失败: " + petName + " (getSave返回null)");
                                       }
                                    }
                                    catch(petError:Error)
                                    {
                                       failedPets++;
                                       debugInfo.push("添加异常: " + petName + " - " + petError.message);
                                       continue;
                                    }
                                 }

                                 // 刷新宠物UI
                                 try
                                 {
                                    if(Gaming.uiGroup.petUI && Gaming.uiGroup.petUI.visible)
                                    {
                                       Gaming.uiGroup.petUI.fleshData();
                                    }
                                 }
                                 catch(uiError:Error)
                                 {
                                    debugInfo.push("UI刷新失败: " + uiError.message);
                                 }

                                 result = "✅ 手动添加宠物：成功 " + addedPets + " 只，失败 " + failedPets + " 只\n调试信息:\n" + debugInfo.slice(0, 10).join("\n");
                              }
                              catch(manualError:Error)
                              {
                                 result = "❌ 手动添加宠物失败: " + manualError.message;
                              }
                           }
                        }
                        catch(e:Error)
                        {
                           result = "❌ 获取宠物失败: " + e.message;
                        }
                        break;
                     case "getsafe":
                        try
                        {
                           // 强制启用GM功能
                           Gaming.testCtrl.enabled = true;
                           Gaming.testCtrl.cheating.enabled = true;

                           // 扩展宠物格子
                           Gaming.PG.da.pet.addBagNum(50);

                           var safeAddedPets:int = 0;
                           var safeFailedPets:int = 0;
                           var safeDebugInfo:Array = [];

                           // 使用更可靠的宠物名称列表
                           var safePetNames:Array = ["BoomSkull", "IronChief", "Lake", "FightWolf", "ZombieWolf"];

                           for each(var safePetName:String in safePetNames)
                           {
                              try
                              {
                                 // 确保有足够的空间
                                 if(Gaming.PG.da.pet.getSpaceNum() <= 0)
                                 {
                                    Gaming.PG.da.pet.addBagNum(5);
                                 }

                                 // 检查基因定义是否存在
                                 var safeGeneDefine:* = Gaming.defineGroup.gene.getDefine(safePetName);
                                 if(!safeGeneDefine)
                                 {
                                    safeDebugInfo.push("跳过未定义的宠物: " + safePetName);
                                    safeFailedPets++;
                                    continue;
                                 }

                                 // 使用最基础的方法创建宠物
                                 var geneSave:* = Gaming.defineGroup.geneCreator.getSave("red", 1, safePetName, true);
                                 if(geneSave)
                                 {
                                    var geneData:* = Gaming.defineGroup.geneCreator.getTempData(geneSave);
                                    if(geneData)
                                    {
                                       var petData:* = Gaming.PG.da.pet.addByGeneData(geneData);
                                       if(petData)
                                       {
                                          safeAddedPets++;
                                          safeDebugInfo.push("成功添加: " + safePetName);
                                       }
                                       else
                                       {
                                          safeFailedPets++;
                                          safeDebugInfo.push("添加失败: " + safePetName + " (addByGeneData返回null)");
                                       }
                                    }
                                    else
                                    {
                                       safeFailedPets++;
                                       safeDebugInfo.push("添加失败: " + safePetName + " (getTempData返回null)");
                                    }
                                 }
                                 else
                                 {
                                    safeFailedPets++;
                                    safeDebugInfo.push("添加失败: " + safePetName + " (getSave返回null)");
                                 }
                              }
                              catch(safeError:Error)
                              {
                                 safeFailedPets++;
                                 safeDebugInfo.push("添加异常: " + safePetName + " - " + safeError.message);
                                 continue;
                              }
                           }

                           // 刷新宠物UI
                           try
                           {
                              if(Gaming.uiGroup.petUI && Gaming.uiGroup.petUI.visible)
                              {
                                 Gaming.uiGroup.petUI.fleshData();
                              }
                           }
                           catch(uiError:Error)
                           {
                              safeDebugInfo.push("UI刷新失败: " + uiError.message);
                           }

                           result = "✅ 安全添加宠物：成功 " + safeAddedPets + " 只，失败 " + safeFailedPets + " 只\n调试信息:\n" + safeDebugInfo.join("\n");
                        }
                        catch(e:Error)
                        {
                           result = "❌ 安全添加宠物失败: " + e.message;
                        }
                        break;
                     case "getfew":
                        try
                        {
                           // 强制启用GM功能
                           Gaming.testCtrl.enabled = true;
                           Gaming.testCtrl.cheating.enabled = true;

                           // 扩展宠物格子
                           Gaming.PG.da.pet.addBagNum(20);

                           var fewAddedPets:int = 0;
                           var fewFailedPets:int = 0;
                           var fewDebugInfo:Array = [];

                           // 使用最基础的宠物列表
                           var fewPetNames:Array = ["BoomSkull", "IronChief", "Lake"];

                           for each(var fewPetName:String in fewPetNames)
                           {
                              try
                              {
                                 // 确保有足够的空间
                                 if(Gaming.PG.da.pet.getSpaceNum() <= 0)
                                 {
                                    Gaming.PG.da.pet.addBagNum(5);
                                 }

                                 // 检查基因定义是否存在
                                 var fewGeneDefine:* = Gaming.defineGroup.gene.getDefine(fewPetName);
                                 if(!fewGeneDefine)
                                 {
                                    fewDebugInfo.push("跳过未定义的宠物: " + fewPetName);
                                    fewFailedPets++;
                                    continue;
                                 }

                                 // 使用最基础的方法
                                 var geneSave:* = Gaming.defineGroup.geneCreator.getSave("red", 1, fewPetName, true);
                                 if(geneSave)
                                 {
                                    var geneData:* = Gaming.defineGroup.geneCreator.getTempData(geneSave);
                                    if(geneData)
                                    {
                                       var petData:* = Gaming.PG.da.pet.addByGeneData(geneData);
                                       if(petData)
                                       {
                                          fewAddedPets++;
                                          fewDebugInfo.push("成功添加: " + fewPetName);
                                       }
                                       else
                                       {
                                          fewFailedPets++;
                                          fewDebugInfo.push("添加失败: " + fewPetName + " (addByGeneData返回null)");
                                       }
                                    }
                                    else
                                    {
                                       fewFailedPets++;
                                       fewDebugInfo.push("添加失败: " + fewPetName + " (getTempData返回null)");
                                    }
                                 }
                                 else
                                 {
                                    fewFailedPets++;
                                    fewDebugInfo.push("添加失败: " + fewPetName + " (getSave返回null)");
                                 }
                              }
                              catch(fewError:Error)
                              {
                                 fewFailedPets++;
                                 fewDebugInfo.push("添加异常: " + fewPetName + " - " + fewError.message);
                                 continue;
                              }
                           }

                           // 刷新宠物UI
                           try
                           {
                              if(Gaming.uiGroup.petUI && Gaming.uiGroup.petUI.visible)
                              {
                                 Gaming.uiGroup.petUI.fleshData();
                              }
                           }
                           catch(uiError:Error)
                           {
                              fewDebugInfo.push("UI刷新失败: " + uiError.message);
                           }

                           result = "✅ 简化添加宠物：成功 " + fewAddedPets + " 只，失败 " + fewFailedPets + " 只\n调试信息:\n" + fewDebugInfo.join("\n");
                        }
                        catch(e:Error)
                        {
                           result = "❌ 简化添加宠物失败: " + e.message;
                        }
                        break;
                     case "debug":
                        try
                        {
                           var debugResult:Array = [];

                           // 检查基因定义组
                           debugResult.push("=== 基因定义组检查 ===");
                           if(Gaming.defineGroup && Gaming.defineGroup.gene)
                           {
                              debugResult.push("✅ 基因定义组存在");

                              // 获取正常宠物名称
                              var normalNames:Array = Gaming.defineGroup.gene.getNormalNameArr();
                              debugResult.push("正常宠物数量: " + normalNames.length);
                              debugResult.push("前10个宠物: " + normalNames.slice(0, 10).join(", "));

                              // 测试几个常见宠物
                              var testPets:Array = ["BoomSkull", "IronChief", "Lake", "FightWolf", "ZombieWolf"];
                              debugResult.push("\n=== 测试宠物定义 ===");
                              for each(var testPet:String in testPets)
                              {
                                 var testDefine:* = Gaming.defineGroup.gene.getDefine(testPet);
                                 if(testDefine)
                                 {
                                    debugResult.push("✅ " + testPet + ": 定义存在，中文名: " + testDefine.cnName);
                                 }
                                 else
                                 {
                                    debugResult.push("❌ " + testPet + ": 定义不存在");
                                 }
                              }
                           }
                           else
                           {
                              debugResult.push("❌ 基因定义组不存在");
                           }

                           // 检查基因创建器
                           debugResult.push("\n=== 基因创建器检查 ===");
                           if(Gaming.defineGroup && Gaming.defineGroup.geneCreator)
                           {
                              debugResult.push("✅ 基因创建器存在");

                              // 测试创建一个宠物
                              try
                              {
                                 var testGeneSave:* = Gaming.defineGroup.geneCreator.getSave("red", 1, "BoomSkull", true);
                                 if(testGeneSave)
                                 {
                                    debugResult.push("✅ 测试创建基因保存成功");

                                    var testGeneData:* = Gaming.defineGroup.geneCreator.getTempData(testGeneSave);
                                    if(testGeneData)
                                    {
                                       debugResult.push("✅ 测试创建基因数据成功");
                                    }
                                    else
                                    {
                                       debugResult.push("❌ 测试创建基因数据失败");
                                    }
                                 }
                                 else
                                 {
                                    debugResult.push("❌ 测试创建基因保存失败");
                                 }
                              }
                              catch(testError:Error)
                              {
                                 debugResult.push("❌ 测试创建异常: " + testError.message);
                              }
                           }
                           else
                           {
                              debugResult.push("❌ 基因创建器不存在");
                           }

                           // 检查宠物背包
                           debugResult.push("\n=== 宠物背包检查 ===");
                           if(Gaming.PG && Gaming.PG.da && Gaming.PG.da.pet)
                           {
                              debugResult.push("✅ 宠物背包存在");
                              debugResult.push("当前宠物数量: " + Gaming.PG.da.pet.arr.length);
                              debugResult.push("背包容量: " + Gaming.PG.da.pet.saveGroup.lockLen);
                              debugResult.push("剩余空间: " + Gaming.PG.da.pet.getSpaceNum());
                           }
                           else
                           {
                              debugResult.push("❌ 宠物背包不存在");
                           }

                           result = debugResult.join("\n");
                        }
                        catch(debugError:Error)
                        {
                           result = "❌ 调试检查失败: " + debugError.message;
                        }
                        break;
                     case "expandbag":
                        try
                        {
                           Gaming.PG.da.pet.addBagNum(100);
                           result = "宠物格子已扩展到 " + Gaming.PG.da.pet.saveGroup.lockLen + " 个！";
                        }
                        catch(e:Error)
                        {
                           result = "扩展宠物格子失败: " + e.message;
                        }
                        break;
                     case "getallcards":
                        Gaming.testCtrl.cheating.doOrder("achieve", "createBosscard", "所有魂卡 5", 0);
                        result = "添加所有5星魂卡";
                        break;
                     case "craftexp":
                        try
                        {
                           Gaming.testCtrl.cheating.doOrder("pet", "addNowCraftExp", "", v);
                           result = "增加飞船经验: " + v;
                        }
                        catch(e:Error)
                        {
                           result = "增加飞船经验失败: " + e.message;
                        }
                        break;
                     case "craftlv":
                        try
                        {
                           Gaming.testCtrl.cheating.doOrder("pet", "setNowCraftLv", "", v);
                           result = "设置飞船等级: " + v;
                        }
                        catch(e:Error)
                        {
                           result = "设置飞船等级失败: " + e.message;
                        }
                        break;
                     case "setlv":
                        Gaming.testCtrl.cheating.doOrder("pet", "setPetLv", "", v);
                        result = "设置当前宠物等级: " + v;
                        break;
                     case "craftexp":
                        Gaming.testCtrl.cheating.doOrder("pet", "addNowCraftExp", "", v);
                        result = "设置飞船经验: " + v;
                        break;
                     case "craftlv":
                        Gaming.testCtrl.cheating.doOrder("pet", "setNowCraftLv", "", v);
                        result = "设置飞船等级: " + v;
                        break;
                     case "testsingle":
                        try
                        {
                           // 扩展宠物格子
                           Gaming.PG.da.pet.addBagNum(10);

                           // 测试添加一个简单的宠物
                           var testPetName:String = "BoomSkull";
                           var testGeneDefine:* = Gaming.defineGroup.gene.getDefine(testPetName);

                           if(testGeneDefine)
                           {
                              var testGeneSave:* = Gaming.defineGroup.geneCreator.getSave("red", Gaming.PG.da.level, testPetName, true);
                              if(testGeneSave)
                              {
                                 var testGeneData:* = Gaming.defineGroup.geneCreator.getTempData(testGeneSave);
                                 if(testGeneData)
                                 {
                                    var testPetData:* = Gaming.PG.da.pet.addByGeneData(testGeneData);
                                    if(testPetData)
                                    {
                                       // 刷新宠物UI
                                       if(Gaming.uiGroup.petUI && Gaming.uiGroup.petUI.visible)
                                       {
                                          Gaming.uiGroup.petUI.fleshData();
                                       }
                                       result = "成功添加测试宠物: " + testGeneDefine.cnName + " (ID: " + testPetName + ")";
                                    }
                                    else
                                    {
                                       result = "添加宠物失败: addByGeneData返回null";
                                    }
                                 }
                                 else
                                 {
                                    result = "创建基因数据失败: getTempData返回null";
                                 }
                              }
                              else
                              {
                                 result = "创建基因保存失败: getSave返回null";
                              }
                           }
                           else
                           {
                              result = "找不到宠物定义: " + testPetName;
                           }
                        }
                        catch(testError:Error)
                        {
                           result = "测试宠物添加失败: " + testError.message + " (堆栈: " + testError.getStackTrace() + ")";
                        }
                        break;
                  }
                  break;
               case "more":
                  switch(action)
                  {
                     case "addgirl":
                        Gaming.testCtrl.cheating.doOrder("more", "addMore", "", 0);
                        result = "添加妞";
                        break;
                  }
                  break;
               case "pay":
                  switch(action)
                  {
                     case "gold":
                        Gaming.testCtrl.cheating.doOrder("pay", "testYue", "", v);
                        result = "设置黄金: " + v;
                        break;
                     case "recharge":
                        Gaming.testCtrl.cheating.doOrder("pay", "pay", "", v);
                        result = "充值: " + v;
                        break;
                     case "showcount":
                        Gaming.testCtrl.cheating.doOrder("pay", "showPayCount", "", 0);
                        result = "显示充值统计";
                        break;
                  }
                  break;
               case "arena":
                  switch(action)
                  {
                     case "num":
                        Gaming.PG.save.arena.todayNum = v;
                        result = "设置竞技场次数: " + v;
                        break;
                     case "score":
                        Gaming.PG.save.arena.score = v;
                        result = "设置竞技场分数: " + v;
                        break;
                  }
                  break;
               case "space":
                  switch(action)
                  {
                     case "level":
                        var craft:* = Gaming.PG.da.space.craft.getNowData();
                        if(craft)
                        {
                           craft.setLevel(v);
                           result = "设置飞船等级: " + v;
                        }
                        else
                        {
                           result = "没有找到飞船数据";
                        }
                        break;
                     case "exp":
                        Gaming.PG.da.space.addNowCraftExp(v);
                        result = "增加飞船经验: " + v;
                        break;
                  }
                  break;
               case "union":
                  switch(action)
                  {
                     case "rank":
                        var militaryDef:* = Gaming.defineGroup.union.military.getDefine(v + "");
                        if(militaryDef)
                        {
                           Gaming.testCtrl.cheating.tempContribution = militaryDef.totalMust;
                           result = "设置军衔等级: " + v + " (" + militaryDef.cnName + ")";
                        }
                        else
                        {
                           result = "军衔等级 " + v + " 不存在";
                        }
                        break;
                     case "army":
                        Gaming.api.union.grow.doTask(Gaming.getSaveIndex(), v + "");
                        result = "快速升级军队到: " + v + "级";
                        break;
                     case "contribution":
                        result = "贡献值功能需要具体数值";
                        break;
                  }
                  break;
               case "achieve":
                  switch(action)
                  {
                     case "unlockall":
                        try
                        {
                           // 强制启用GM功能
                           Gaming.testCtrl.enabled = true;
                           Gaming.testCtrl.cheating.enabled = true;

                           // 方法1: 使用GM命令完成所有成就
                           try
                           {
                              Gaming.testCtrl.cheating.doOrder("achieve", "completeAllAchieve", "", 0);
                              result = "✅ 通过GM命令解锁全部成就成功";
                           }
                           catch(gmError:Error)
                           {
                              // 方法2: 直接调用成就系统
                              try
                              {
                                 Gaming.PG.da.achieve.completeAll();
                                 Gaming.PG.da.achieve.fleshAffterComplete();
                                 result = "✅ 通过成就系统解锁全部成就成功";
                              }
                              catch(systemError:Error)
                              {
                                 // 方法3: 手动遍历所有成就
                                 var achieveGroup:* = Gaming.PG.da.achieve;
                                 var allAchievements:Object = achieveGroup.obj;
                                 var completedCount:int = 0;

                                 for(var achieveName:String in allAchievements)
                                 {
                                    var achieveData:* = allAchievements[achieveName];
                                    if(achieveData && !achieveData.isCompleteB())
                                    {
                                       try
                                       {
                                          achieveData.complete();
                                          completedCount++;
                                       }
                                       catch(completeError:Error)
                                       {
                                          continue;
                                       }
                                    }
                                 }

                                 achieveGroup.fleshAffterComplete();
                                 result = "✅ 手动完成 " + completedCount + " 个成就";
                              }
                           }

                           // 强制刷新成就界面
                           try
                           {
                              if(Gaming.uiGroup.achieveUI && Gaming.uiGroup.achieveUI.visible)
                              {
                                 Gaming.uiGroup.achieveUI.hide();
                                 TweenLite.delayedCall(0.1, function():void {
                                    Gaming.uiGroup.achieveUI.show();
                                 });
                              }
                           }
                           catch(uiError:Error)
                           {
                              // UI刷新失败不影响主要功能
                           }
                        }
                        catch(e:Error)
                        {
                           result = "❌ 解锁成就失败: " + e.message;
                        }
                        break;
                     case "forceall":
                        try
                        {
                           // 强制启用GM功能
                           Gaming.testCtrl.enabled = true;
                           Gaming.testCtrl.cheating.enabled = true;

                           var achieveGroup:* = Gaming.PG.da.achieve;
                           var completedCount:int = 0;
                           var errorCount:int = 0;

                           // 方法1: 尝试使用系统方法
                           try
                           {
                              achieveGroup.completeAll();
                              achieveGroup.fleshAffterComplete();
                              result = "🎉 通过系统方法强制完成所有成就";
                           }
                           catch(systemError:Error)
                           {
                              // 方法2: 手动遍历所有成就
                              var allAchievements:Object = achieveGroup.obj;

                              for(var achieveName:String in allAchievements)
                              {
                                 var achieveData:* = allAchievements[achieveName];
                                 if(achieveData)
                                 {
                                    try
                                    {
                                       // 检查是否已完成
                                       if(!achieveData.isCompleteB())
                                       {
                                          // 强制设置为完成状态
                                          if(achieveData.save)
                                          {
                                             achieveData.save.state = "complete";
                                             achieveData.newB = true;
                                          }
                                          else
                                          {
                                             // 如果没有save，创建一个
                                             achieveData.complete();
                                          }
                                          completedCount++;
                                       }
                                    }
                                    catch(achieveError:Error)
                                    {
                                       errorCount++;
                                       continue;
                                    }
                                 }
                              }

                              // 刷新成就数据
                              try
                              {
                                 achieveGroup.fleshAffterComplete();
                              }
                              catch(fleshError:Error)
                              {
                                 // 忽略刷新错误
                              }

                              result = "🎉 强制完成 " + completedCount + " 个成就，失败 " + errorCount + " 个";
                           }

                           // 强制刷新成就界面
                           try
                           {
                              if(Gaming.uiGroup.achieveUI && Gaming.uiGroup.achieveUI.visible)
                              {
                                 Gaming.uiGroup.achieveUI.hide();
                                 TweenLite.delayedCall(0.1, function():void {
                                    Gaming.uiGroup.achieveUI.show();
                                 });
                              }
                           }
                           catch(uiError:Error)
                           {
                              // UI刷新失败不影响主要功能
                           }
                        }
                        catch(e:Error)
                        {
                           result = "❌ 强制完成成就失败: " + e.message;
                        }
                        break;
                     case "kill":
                        Gaming.PG.SAVE.count.killNum = v;
                        result = "设置杀敌数: " + v;
                        break;
                     case "noorange":
                        Gaming.PG.SAVE.count.bossNoOrangeNum = v;
                        result = "设置不掉橙装数: " + v;
                        break;
                     case "king":
                        Gaming.PG.SAVE.count.maxKingLevel = v;
                        result = "设置擒王等级: " + v;
                        break;
                     case "coin":
                        Gaming.PG.SAVE.count.lotteryCoin = v;
                        result = "设置全是银币次数: " + v;
                        break;
                     case "orange":
                        Gaming.PG.save.count.lotteryAllOrangeNum = v;
                        result = "设置全是橙装次数: " + v;
                        break;
                     case "reset":
                        Gaming.PG.da.achieve.clearData();
                        result = "重置成就数据";
                        break;
                  }
                  break;
               case "time":
                  switch(action)
                  {
                     case "speed":
                        CountCtrl.onlineTimePer = v;
                        result = "设置时间加速倍数: " + v;
                        break;
                  }
                  break;
               case "character":
                  switch(action)
                  {
                     case "level99":
                        Gaming.testCtrl.cheating.doOrder("player", "setHeroLv", "", 99);
                        result = "设置角色等级99";
                        break;
                     case "maxlove":
                        try
                        {
                           // 设置所有队友的好感度
                           var moreArr:Array = Gaming.PG.da.more.dataArr;
                           var moreBagArr:Array = Gaming.PG.da.moreBag.dataArr;
                           var allMoreArr:Array = moreArr.concat(moreBagArr);
                           var loveData:*;
                           var setCount:int = 0;

                           for each(var moreData:* in allMoreArr)
                           {
                              if(moreData && moreData.love)
                              {
                                 loveData = moreData.love;
                                 loveData.addValue(9999 - loveData.getValue());
                                 setCount++;
                              }
                           }
                           result = "设置了 " + setCount + " 个角色的满好感";
                        }
                        catch(e:Error)
                        {
                           result = "设置好感度失败: " + e.message;
                        }
                        break;
                     case "maxstats":
                        Gaming.PG.da.peak.save.lv = 999;
                        result = "设置满属性";
                        break;
                     case "reset":
                        Gaming.PG.da.love.clearData();
                        result = "重置角色数据";
                        break;
                     case "infinitepower":
                        try
                        {
                           // 设置所有队友的体力
                           var moreArr2:Array = Gaming.PG.da.more.dataArr;
                           var moreBagArr2:Array = Gaming.PG.da.moreBag.dataArr;
                           var allMoreArr2:Array = moreArr2.concat(moreBagArr2);
                           var powerCount:int = 0;

                           for each(var moreData2:* in allMoreArr2)
                           {
                              if(moreData2 && moreData2.partner && moreData2.partner.save)
                              {
                                 moreData2.partner.save.power = 999;
                                 powerCount++;
                              }
                           }
                           result = "设置了 " + powerCount + " 个角色的无限体力";
                        }
                        catch(e:Error)
                        {
                           result = "设置体力失败: " + e.message;
                        }
                        break;
                  }
                  break;
               case "other":
                  switch(action)
                  {
                     case "exploit":
                        Gaming.testCtrl.cheating.doOrder("other", "setPartnerExploit", "", v);
                        result = "设置队友功勋: " + v;
                        break;
                     case "clearpets":
                        try
                        {
                           Gaming.PG.da.pet.clearData();
                           result = "清除尸宠背包成功！";
                        }
                        catch(e:Error)
                        {
                           result = "清除尸宠背包失败: " + e.message;
                        }
                        break;
                     case "setcheat":
                        try
                        {
                           Gaming.PG.save.main.isZuobiB = true;
                           result = "设置存档异常成功！";
                        }
                        catch(e:Error)
                        {
                           result = "设置存档异常失败: " + e.message;
                        }
                        break;
                     case "clearoverflow":
                        try
                        {
                           Gaming.PG.DATA.arms.delNoPositionItems();
                           result = "清除溢出物品成功！";
                        }
                        catch(e:Error)
                        {
                           result = "清除溢出物品失败: " + e.message;
                        }
                        break;
                  }
                  break;
               case "time":
                  switch(action)
                  {
                     case "clearall":
                        try
                        {
                           Gaming.PG.da.time.clearAllTime();
                           result = "清除所有双倍时间成功！";
                        }
                        catch(e:Error)
                        {
                           result = "清除所有双倍时间失败: " + e.message;
                        }
                        break;
                  }
                  break;
               case "custom":
                  switch(action)
                  {
                     case "addsystem":
                        showCustomAddSystem();
                        result = "打开自定义添加系统";
                        break;
                     case "card":
                        showCustomCardEditor();
                        result = "🎴 打开自定义魂卡属性编辑器";
                        break;
                     case "equip":
                        showCustomEquipEditor();
                        result = "⚔️ 打开自定义装备属性编辑器";
                        break;
                     case "weapon":
                        showCustomWeaponEditor();
                        result = "🔫 打开自定义武器属性编辑器";
                        break;
                     case "vehicle":
                        showCustomVehicleEditor();
                        result = "🚗 打开自定义载具属性编辑器";
                        break;
                     case "skill":
                        showCustomSkillEditor();
                        result = "✨ 打开自定义技能属性编辑器";
                        break;
                     case "getcard":
                        getCustomCard();
                        result = "🎁 获取自定义魂卡";
                        break;
                     case "getequip":
                        getCustomEquip();
                        result = "📦 获取自定义装备";
                        break;
                     case "getweapon":
                        getCustomWeapon();
                        result = "🎯 获取自定义武器";
                        break;
                     case "getvehicle":
                        getCustomVehicle();
                        result = "🚀 获取自定义载具";
                        break;
                     case "getskill":
                        getCustomSkill();
                        result = "⭐ 获取自定义技能";
                        break;
                  }
                  break;
               case "export":
                  switch(action)
                  {
                     case "allids":
                        result = "导出功能：请查看剪贴板";
                        try
                        {
                           exportAllGameIDs();
                        }
                        catch(e:Error)
                        {
                           result = "导出失败: " + e.message;
                        }
                        break;
                  }
                  break;
               case "tower":
                  switch(action)
                  {
                     case "complete":
                        try
                        {
                           var towerData:* = Gaming.PG.da.tower;
                           if(towerData && towerData.getSave())
                           {
                              var towerSave:* = towerData.getSave();
                              var towerDefines:Array = Gaming.defineGroup.tower.getUIArr();
                              var completedCount:int = 0;

                              // 遍历所有塔层，设置为最高难度通关
                              for each(var towerDefine:* in towerDefines)
                              {
                                 if(towerDefine && towerDefine.getName)
                                 {
                                    var towerName:String = towerDefine.getName();
                                    var maxDiff:int = 4; // TowerDefine.MAX_DIFF 通常是4
                                    towerSave.winEvent(towerName, maxDiff);
                                    towerSave.giftEvent(towerName, maxDiff);
                                    completedCount++;
                                 }
                              }

                              // 同时通关天塔（无尽模式）
                              var unendData:* = towerData.unend;
                              if(unendData && unendData.getSave())
                              {
                                 var unendSave:* = unendData.getSave();
                                 var maxUnendLevel:int = 95; // UnendAgent.MAX_LV
                                 unendSave.unendLv = maxUnendLevel;
                                 unendSave.uP = 0;
                                 result = "🗼 虚天塔通关成功！已通关 " + completedCount + " 个幻塔层，天塔已通关至第 " + maxUnendLevel + " 层！";
                              }
                              else
                              {
                                 result = "🗼 幻塔通关成功！已通关 " + completedCount + " 个塔层，但天塔数据不可用！";
                              }
                           }
                           else
                           {
                              result = "🗼 虚天塔数据不可用，请先进入游戏";
                           }
                        }
                        catch(e:Error)
                        {
                           result = "🗼 通关虚天塔失败: " + e.message;
                        }
                        break;
                     case "all":
                        // 一键通关虚天塔
                        try
                        {
                           var towerData:* = Gaming.PG.da.tower;
                           if(towerData && towerData.getSave())
                           {
                              var towerSave:* = towerData.getSave();
                              var towerDefines:Array = Gaming.defineGroup.tower.getUIArr();
                              var completedCount:int = 0;

                              // 遍历所有塔层，设置为最高难度通关
                              for each(var towerDefine:* in towerDefines)
                              {
                                 if(towerDefine && towerDefine.getName)
                                 {
                                    var towerName:String = towerDefine.getName();
                                    var maxDiff:int = 4; // TowerDefine.MAX_DIFF 通常是4
                                    towerSave.winEvent(towerName, maxDiff);
                                    towerSave.giftEvent(towerName, maxDiff);
                                    completedCount++;
                                 }
                              }

                              // 同时通关天塔（无尽模式）
                              var unendData:* = towerData.unend;
                              if(unendData && unendData.getSave())
                              {
                                 var unendSave:* = unendData.getSave();
                                 var maxUnendLevel:int = 95; // UnendAgent.MAX_LV
                                 unendSave.unendLv = maxUnendLevel;
                                 unendSave.uP = 0;
                                 result = "🗼 一键通关虚天塔成功！已通关 " + completedCount + " 个幻塔层，天塔已通关至第 " + maxUnendLevel + " 层，全部设为最高难度！";
                              }
                              else
                              {
                                 result = "🗼 一键通关幻塔成功！已通关 " + completedCount + " 个塔层，全部设为最高难度，但天塔数据不可用！";
                              }
                           }
                           else
                           {
                              result = "🗼 虚天塔数据不可用，请先进入游戏";
                           }
                        }
                        catch(e:Error)
                        {
                           result = "🗼 通关虚天塔失败: " + e.message;
                        }
                        break;
                  }
                  break;

            }

            addOutput(result);
         }
         catch(error:Error)
         {
            addOutput("执行错误: " + error.message);
         }
      }

      public function show():void
      {
         if (!isVisible)
         {
            isVisible = true;
            this.visible = true;
            this.x = (Gaming.WIDTH - 900) / 2;
            this.y = (Gaming.HEIGHT - 600) / 2;
         }
      }

      public function hide(e:MouseEvent = null):void
      {
         if (isVisible)
         {
            isVisible = false;
            this.visible = false;
         }
      }

      public function toggle():void
      {
         if (isVisible)
         {
            hide();
         }
         else
         {
            show();
         }
      }

      public function handleKeyDown(e:KeyboardEvent):void
      {
         if(!isVisible) return;

         if(e.keyCode == Keyboard.ESCAPE) // ESC键
         {
            if(inputContainer && inputContainer.visible)
            {
               inputContainer.visible = false;
               addOutput("❌ 取消输入");
            }
         }
         else if(e.keyCode == Keyboard.ENTER) // 回车键
         {
            if(inputContainer && inputContainer.visible)
            {
               onInputConfirm(null);
            }
         }
      }

      private function addOutput(text:String):void
      {
         outputText.appendText(text + "\n");
         outputText.scrollV = outputText.maxScrollV;
      }

      private function exportAllGameIDs():void
      {
         try
         {
            var exportText:String = "=== 游戏完整ID数据导出 ===\n";
            exportText += "导出时间: " + new Date().toString() + "\n\n";

            // 导出物品ID
            exportText += this.exportCategoryIDs("物品 Things", "things");

            // 导出装备ID
            exportText += this.exportCategoryIDs("装备 Equipment", "equip");

            // 导出武器ID
            exportText += this.exportCategoryIDs("武器 Arms", "bullet");

            // 导出宠物/基因ID
            exportText += this.exportCategoryIDs("宠物/基因 Pets/Genes", "gene");

            // 导出技能ID
            exportText += this.exportCategoryIDs("技能 Skills", "skill");

            // 导出零件ID
            exportText += this.exportCategoryIDs("零件 Parts", "parts");

            // 导出装置ID
            exportText += this.exportCategoryIDs("装置 Devices", "device");

            // 导出兵器ID
            exportText += this.exportCategoryIDs("兵器 Weapons", "weapon");

            // 导出时装ID
            exportText += this.exportFashionIDs();

            // 导出地图ID
            exportText += this.exportMapIDs();

            // 导出敌人ID
            exportText += this.exportEnemyIDs();

            // 导出礼品ID
            exportText += this.exportCategoryIDs("礼品 Gifts", "gift");

            // 导出成就ID
            exportText += this.exportCategoryIDs("成就 Achievements", "achieve");

            // 导出任务ID
            exportText += this.exportCategoryIDs("任务 Missions", "mission");

            // 导出商店ID
            exportText += this.exportCategoryIDs("商店 Shops", "shop");

            // 导出音效ID
            exportText += this.exportSoundIDs();

            // 导出UI相关ID
            exportText += this.exportUIIDs();

            // 导出所有定义组的完整信息
            exportText += this.exportAllDefineGroups();

            // 尝试导出一些基础信息
            try
            {
               if(Gaming.defineGroup)
               {
                  exportText += "defineGroup 可用\n";

                  // 列出所有可用的定义组
                  for(var groupName:String in Gaming.defineGroup)
                  {
                     try
                     {
                        var group:* = Gaming.defineGroup[groupName];
                        if(group)
                        {
                           exportText += "定义组: " + groupName + " - 可用\n";

                           // 如果有obj属性，尝试获取一些ID
                           if(group.obj)
                           {
                              var count:int = 0;
                              for(var id:String in group.obj)
                              {
                                 if(count < 5) // 只显示前5个作为示例
                                 {
                                    exportText += "  示例ID: " + id + "\n";
                                    count++;
                                 }
                                 else
                                 {
                                    break;
                                 }
                              }
                           }

                           // 如果有arr属性，显示数组长度
                           if(group.arr && group.arr.length)
                           {
                              exportText += "  数组长度: " + group.arr.length + "\n";
                           }
                        }
                     }
                     catch(e:Error)
                     {
                        exportText += "定义组: " + groupName + " - 访问错误: " + e.message + "\n";
                     }
                  }
               }
               else
               {
                  exportText += "Gaming.defineGroup 不可用\n";
               }
            }
            catch(e:Error)
            {
               exportText += "访问defineGroup时出错: " + e.message + "\n";
            }

            exportText += "\n=== 导出完成 ===\n";
            exportText += "基础数据导出完成\n";

            // 复制到剪贴板
            System.setClipboard(exportText);

            addOutput("✅ 基础数据已导出并复制到剪贴板");
            addOutput("� 可以粘贴查看详细信息");

         }
         catch(error:Error)
         {
            addOutput("❌ 导出失败: " + error.message);
            // 至少提供一些基础信息
            var basicInfo:String = "=== 基础信息 ===\n";
            basicInfo += "导出时间: " + new Date().toString() + "\n";
            basicInfo += "错误信息: " + error.message + "\n";
            System.setClipboard(basicInfo);
         }
      }

      // 通用的分类ID导出方法
      private function exportCategoryIDs(categoryName:String, groupKey:String):String
      {
         var result:String = "\n=== " + categoryName + " ===\n";
         try
         {
            if(Gaming.defineGroup && Gaming.defineGroup[groupKey])
            {
               var group:* = Gaming.defineGroup[groupKey];
               var count:int = 0;

               // 尝试从obj属性导出
               if(group.obj)
               {
                  for(var id:String in group.obj)
                  {
                     try
                     {
                        var def:* = group.obj[id];
                        var cnName:String = "未知";
                        if(def && def.cnName)
                        {
                           cnName = def.cnName;
                        }
                        result += "ID: " + id + " | 名称: " + cnName + "\n";
                        count++;
                     }
                     catch(e:Error)
                     {
                        result += "ID: " + id + " | 名称: 解析错误\n";
                        count++;
                     }
                  }
               }

               // 尝试从arr属性导出
               if(group.arr && group.arr.length > 0)
               {
                  for(var i:int = 0; i < group.arr.length; i++)
                  {
                     try
                     {
                        var arrDef:* = group.arr[i];
                        if(arrDef)
                        {
                           var arrId:String = arrDef.name || arrDef.id || ("index_" + i);
                           var arrCnName:String = arrDef.cnName || "未知";
                           result += "ID: " + arrId + " | 名称: " + arrCnName + "\n";
                           count++;
                        }
                     }
                     catch(e:Error)
                     {
                        result += "ID: index_" + i + " | 名称: 解析错误\n";
                        count++;
                     }
                  }
               }

               result += "总计: " + count + " 个\n";
            }
            else
            {
               result += categoryName + " 数据不可用\n";
            }
         }
         catch(e:Error)
         {
            result += categoryName + " 导出错误: " + e.message + "\n";
         }
         return result;
      }

      // 导出时装ID
      private function exportFashionIDs():String
      {
         var result:String = "\n=== 时装 Fashion ===\n";
         try
         {
            if(Gaming.defineGroup && Gaming.defineGroup.equip && Gaming.defineGroup.equip.fashionObj)
            {
               var count:int = 0;
               for(var fashionId:String in Gaming.defineGroup.equip.fashionObj)
               {
                  try
                  {
                     var fashionDef:* = Gaming.defineGroup.equip.fashionObj[fashionId];
                     var fashionName:String = fashionDef && fashionDef.cnName ? fashionDef.cnName : "未知";
                     result += "ID: " + fashionId + " | 名称: " + fashionName + "\n";
                     count++;
                  }
                  catch(e:Error)
                  {
                     result += "ID: " + fashionId + " | 名称: 解析错误\n";
                     count++;
                  }
               }
               result += "总计: " + count + " 个\n";
            }
            else
            {
               result += "时装数据不可用\n";
            }
         }
         catch(e:Error)
         {
            result += "时装导出错误: " + e.message + "\n";
         }
         return result;
      }

      // 导出地图ID
      private function exportMapIDs():String
      {
         var result:String = "\n=== 地图 Maps ===\n";
         try
         {
            if(Gaming.defineGroup && Gaming.defineGroup.map)
            {
               var count:int = 0;

               // 导出地图对象
               if(Gaming.defineGroup.map.obj)
               {
                  for(var mapId:String in Gaming.defineGroup.map.obj)
                  {
                     try
                     {
                        var mapDef:* = Gaming.defineGroup.map.obj[mapId];
                        var mapName:String = mapDef && mapDef.cnName ? mapDef.cnName : "未知";
                        result += "ID: " + mapId + " | 名称: " + mapName + "\n";
                        count++;
                     }
                     catch(e:Error)
                     {
                        result += "ID: " + mapId + " | 名称: 解析错误\n";
                        count++;
                     }
                  }
               }

               result += "总计: " + count + " 个\n";
            }
            else
            {
               result += "地图数据不可用\n";
            }
         }
         catch(e:Error)
         {
            result += "地图导出错误: " + e.message + "\n";
         }
         return result;
      }

      // 导出敌人ID
      private function exportEnemyIDs():String
      {
         var result:String = "\n=== 敌人 Enemies ===\n";
         try
         {
            if(Gaming.defineGroup && Gaming.defineGroup.enemy)
            {
               var count:int = 0;

               if(Gaming.defineGroup.enemy.obj)
               {
                  for(var enemyId:String in Gaming.defineGroup.enemy.obj)
                  {
                     try
                     {
                        var enemyDef:* = Gaming.defineGroup.enemy.obj[enemyId];
                        var enemyName:String = enemyDef && enemyDef.cnName ? enemyDef.cnName : "未知";
                        result += "ID: " + enemyId + " | 名称: " + enemyName + "\n";
                        count++;
                     }
                     catch(e:Error)
                     {
                        result += "ID: " + enemyId + " | 名称: 解析错误\n";
                        count++;
                     }
                  }
               }

               result += "总计: " + count + " 个\n";
            }
            else
            {
               result += "敌人数据不可用\n";
            }
         }
         catch(e:Error)
         {
            result += "敌人导出错误: " + e.message + "\n";
         }
         return result;
      }

      // 导出音效ID
      private function exportSoundIDs():String
      {
         var result:String = "\n=== 音效 Sounds ===\n";
         try
         {
            if(Gaming.defineGroup && Gaming.defineGroup.sound)
            {
               var count:int = 0;

               if(Gaming.defineGroup.sound.obj)
               {
                  for(var soundId:String in Gaming.defineGroup.sound.obj)
                  {
                     try
                     {
                        var soundDef:* = Gaming.defineGroup.sound.obj[soundId];
                        var soundName:String = soundDef && soundDef.cnName ? soundDef.cnName : "未知";
                        result += "ID: " + soundId + " | 名称: " + soundName + "\n";
                        count++;
                     }
                     catch(e:Error)
                     {
                        result += "ID: " + soundId + " | 名称: 解析错误\n";
                        count++;
                     }
                  }
               }

               result += "总计: " + count + " 个\n";
            }
            else
            {
               result += "音效数据不可用\n";
            }
         }
         catch(e:Error)
         {
            result += "音效导出错误: " + e.message + "\n";
         }
         return result;
      }

      // 导出UI相关ID
      private function exportUIIDs():String
      {
         var result:String = "\n=== UI界面 UI ===\n";
         try
         {
            if(Gaming.defineGroup && Gaming.defineGroup.ui)
            {
               var count:int = 0;

               if(Gaming.defineGroup.ui.obj)
               {
                  for(var uiId:String in Gaming.defineGroup.ui.obj)
                  {
                     try
                     {
                        var uiDef:* = Gaming.defineGroup.ui.obj[uiId];
                        var uiName:String = uiDef && uiDef.cnName ? uiDef.cnName : "未知";
                        result += "ID: " + uiId + " | 名称: " + uiName + "\n";
                        count++;
                     }
                     catch(e:Error)
                     {
                        result += "ID: " + uiId + " | 名称: 解析错误\n";
                        count++;
                     }
                  }
               }

               result += "总计: " + count + " 个\n";
            }
            else
            {
               result += "UI数据不可用\n";
            }
         }
         catch(e:Error)
         {
            result += "UI导出错误: " + e.message + "\n";
         }
         return result;
      }

      // 导出所有定义组的完整信息
      private function exportAllDefineGroups():String
      {
         var result:String = "\n=== 所有定义组 All Define Groups ===\n";
         try
         {
            if(Gaming.defineGroup)
            {
               result += "可用的定义组列表:\n";

               for(var groupName:String in Gaming.defineGroup)
               {
                  try
                  {
                     var group:* = Gaming.defineGroup[groupName];
                     if(group)
                     {
                        result += "\n定义组: " + groupName + "\n";

                        // 检查obj属性
                        if(group.obj)
                        {
                           var objCount:int = 0;
                           for(var objId:String in group.obj)
                           {
                              objCount++;
                           }
                           result += "  - obj属性: " + objCount + " 个ID\n";
                        }

                        // 检查arr属性
                        if(group.arr && group.arr.length)
                        {
                           result += "  - arr属性: " + group.arr.length + " 个元素\n";
                        }

                        // 检查其他常见属性
                        if(group.normalNameArr && group.normalNameArr.length)
                        {
                           result += "  - normalNameArr: " + group.normalNameArr.length + " 个\n";
                        }

                        if(group.fashionObj)
                        {
                           var fashionCount:int = 0;
                           for(var fashionId:String in group.fashionObj)
                           {
                              fashionCount++;
                           }
                           result += "  - fashionObj: " + fashionCount + " 个\n";
                        }
                     }
                  }
                  catch(e:Error)
                  {
                     result += "定义组 " + groupName + " 访问错误: " + e.message + "\n";
                  }
               }
            }
            else
            {
               result += "Gaming.defineGroup 不可用\n";
            }
         }
         catch(e:Error)
         {
            result += "定义组导出错误: " + e.message + "\n";
         }
         return result;
      }

      // 自定义添加系统
      private function showCustomAddSystem():void
      {
         try
         {
            // 创建自定义添加窗口
            var customWindow:Sprite = new Sprite();
            customWindow.name = "customAddWindow";

            // 背景
            var bg:Sprite = new Sprite();
            bg.graphics.beginFill(0x000000, 0.8);
            bg.graphics.drawRect(0, 0, 800, 600);
            bg.graphics.endFill();
            customWindow.addChild(bg);

            // 标题
            var title:TextField = new TextField();
            title.text = "🎯 自定义添加系统 - 支持所有游戏ID";
            title.textColor = 0xFFFFFF;
            title.width = 780;
            title.height = 30;
            title.x = 10;
            title.y = 10;
            customWindow.addChild(title);

            // 分类选择
            var categoryLabel:TextField = new TextField();
            categoryLabel.text = "选择分类:";
            categoryLabel.textColor = 0xFFFFFF;
            categoryLabel.width = 100;
            categoryLabel.height = 20;
            categoryLabel.x = 10;
            categoryLabel.y = 50;
            customWindow.addChild(categoryLabel);

            // 分类下拉框（简化版）
            var categories:Array = ["物品", "装备", "武器", "宠物", "技能", "零件", "装置", "兵器", "时装"];
            var categoryY:int = 80;
            for(var i:int = 0; i < categories.length; i++)
            {
               var catBtn:Sprite = createCustomButton(categories[i], 0x0066FF, 80, 25);
               catBtn.x = 10 + (i % 4) * 90;
               catBtn.y = categoryY + Math.floor(i / 4) * 30;
               catBtn.name = "cat_" + categories[i];
               customWindow.addChild(catBtn);
            }

            // ID搜索框
            var searchLabel:TextField = new TextField();
            searchLabel.text = "搜索ID (支持中文名称):";
            searchLabel.textColor = 0xFFFFFF;
            searchLabel.width = 200;
            searchLabel.height = 20;
            searchLabel.x = 10;
            searchLabel.y = 180;
            customWindow.addChild(searchLabel);

            // 搜索输入框
            var searchInput:TextField = new TextField();
            searchInput.type = TextFieldType.INPUT;
            searchInput.border = true;
            searchInput.borderColor = 0xFFFFFF;
            searchInput.background = true;
            searchInput.backgroundColor = 0x333333;
            searchInput.textColor = 0xFFFFFF;
            searchInput.width = 300;
            searchInput.height = 25;
            searchInput.x = 10;
            searchInput.y = 205;
            searchInput.name = "searchInput";
            customWindow.addChild(searchInput);

            // 搜索按钮
            var searchBtn:Sprite = createCustomButton("搜索", 0x00AA00, 60, 25);
            searchBtn.x = 320;
            searchBtn.y = 205;
            searchBtn.name = "searchBtn";
            customWindow.addChild(searchBtn);

            // 数量输入
            var countLabel:TextField = new TextField();
            countLabel.text = "数量:";
            countLabel.textColor = 0xFFFFFF;
            countLabel.width = 50;
            countLabel.height = 20;
            countLabel.x = 400;
            countLabel.y = 180;
            customWindow.addChild(countLabel);

            var countInput:TextField = new TextField();
            countInput.type = TextFieldType.INPUT;
            countInput.border = true;
            countInput.borderColor = 0xFFFFFF;
            countInput.background = true;
            countInput.backgroundColor = 0x333333;
            countInput.textColor = 0xFFFFFF;
            countInput.width = 80;
            countInput.height = 25;
            countInput.x = 400;
            countInput.y = 205;
            countInput.text = "1";
            countInput.name = "countInput";
            customWindow.addChild(countInput);

            // 结果显示区域
            var resultArea:TextField = new TextField();
            resultArea.border = true;
            resultArea.borderColor = 0xFFFFFF;
            resultArea.background = true;
            resultArea.backgroundColor = 0x222222;
            resultArea.textColor = 0xFFFFFF;
            resultArea.width = 780;
            resultArea.height = 300;
            resultArea.x = 10;
            resultArea.y = 250;
            resultArea.wordWrap = true;
            resultArea.text = "在上方选择分类或搜索ID，结果将显示在这里...";
            resultArea.name = "resultArea";
            customWindow.addChild(resultArea);

            // 添加按钮
            var addBtn:Sprite = createCustomButton("添加选中物品", 0xFF6600, 120, 30);
            addBtn.x = 10;
            addBtn.y = 560;
            addBtn.name = "addBtn";
            customWindow.addChild(addBtn);

            // 关闭按钮
            var closeBtn:Sprite = createCustomButton("关闭", 0xFF0000, 60, 30);
            closeBtn.x = 720;
            closeBtn.y = 560;
            closeBtn.name = "closeBtn";
            customWindow.addChild(closeBtn);

            // 添加到舞台
            customWindow.x = (stage.stageWidth - 800) / 2;
            customWindow.y = (stage.stageHeight - 600) / 2;
            stage.addChild(customWindow);

            // 添加事件监听
            customWindow.addEventListener(MouseEvent.CLICK, onCustomWindowClick);

            addOutput("✅ 自定义添加系统已打开");
            addOutput("💡 支持按分类浏览或直接搜索ID");
         }
         catch(e:Error)
         {
            addOutput("❌ 打开自定义添加系统失败: " + e.message);
         }
      }

      // 创建自定义按钮
      private function createCustomButton(text:String, color:uint, width:int, height:int):Sprite
      {
         var btn:Sprite = new Sprite();

         // 背景
         btn.graphics.beginFill(color);
         btn.graphics.drawRoundRect(0, 0, width, height, 5);
         btn.graphics.endFill();

         // 文字
         var label:TextField = new TextField();
         label.text = text;
         label.textColor = 0xFFFFFF;
         label.width = width;
         label.height = height;
         label.mouseEnabled = false;
         btn.addChild(label);

         btn.buttonMode = true;
         btn.useHandCursor = true;

         return btn;
      }

      // 自定义窗口点击事件
      private function onCustomWindowClick(e:MouseEvent):void
      {
         var target:DisplayObject = e.target as DisplayObject;
         var window:Sprite = e.currentTarget as Sprite;

         if(target.name == "closeBtn")
         {
            // 关闭窗口
            window.removeEventListener(MouseEvent.CLICK, onCustomWindowClick);
            stage.removeChild(window);
            addOutput("自定义添加系统已关闭");
         }
         else if(target.name.indexOf("cat_") == 0)
         {
            // 分类按钮点击
            var category:String = target.name.substr(4);
            showCategoryItems(window, category);
         }
         else if(target.name == "searchBtn")
         {
            // 搜索按钮点击
            var searchInput:TextField = window.getChildByName("searchInput") as TextField;
            searchItems(window, searchInput.text);
         }
         else if(target.name == "addBtn")
         {
            // 添加按钮点击
            addSelectedItems(window);
         }
      }

      // 显示分类物品
      private function showCategoryItems(window:Sprite, category:String):void
      {
         var resultArea:TextField = window.getChildByName("resultArea") as TextField;
         var items:Array = getCategoryItems(category);

         var result:String = "=== " + category + " 分类 ===\n";
         result += "找到 " + items.length + " 个物品:\n\n";

         for(var i:int = 0; i < Math.min(items.length, 50); i++) // 限制显示50个
         {
            var item:Object = items[i];
            result += (i + 1) + ". ID: " + item.id + " | 名称: " + item.name + "\n";
         }

         if(items.length > 50)
         {
            result += "\n... 还有 " + (items.length - 50) + " 个物品，请使用搜索功能查找具体物品";
         }

         resultArea.text = result;
         resultArea.scrollV = 1; // 滚动到顶部
      }

      // 搜索物品
      private function searchItems(window:Sprite, searchText:String):void
      {
         var resultArea:TextField = window.getChildByName("resultArea") as TextField;

         if(!searchText || searchText.length < 1)
         {
            resultArea.text = "请输入搜索关键词";
            return;
         }

         var allItems:Array = getAllGameItems();
         var matchedItems:Array = [];

         // 搜索匹配的物品
         for each(var item:Object in allItems)
         {
            if(item.id.toLowerCase().indexOf(searchText.toLowerCase()) >= 0 ||
               item.name.toLowerCase().indexOf(searchText.toLowerCase()) >= 0)
            {
               matchedItems.push(item);
            }
         }

         var result:String = "=== 搜索结果: \"" + searchText + "\" ===\n";
         result += "找到 " + matchedItems.length + " 个匹配物品:\n\n";

         for(var i:int = 0; i < Math.min(matchedItems.length, 30); i++)
         {
            var item:Object = matchedItems[i];
            result += (i + 1) + ". ID: " + item.id + " | 名称: " + item.name + " | 类型: " + item.type + "\n";
         }

         if(matchedItems.length > 30)
         {
            result += "\n... 还有 " + (matchedItems.length - 30) + " 个匹配结果，请使用更具体的关键词";
         }

         resultArea.text = result;
         resultArea.scrollV = 1;
      }

      // 添加选中物品
      private function addSelectedItems(window:Sprite):void
      {
         var resultArea:TextField = window.getChildByName("resultArea") as TextField;
         var countInput:TextField = window.getChildByName("countInput") as TextField;

         var count:int = parseInt(countInput.text) || 1;
         if(count < 1) count = 1;
         if(count > 999) count = 999;

         // 这里需要解析选中的物品ID并添加
         // 简化版本：从结果文本中提取第一个ID
         var text:String = resultArea.text;
         var lines:Array = text.split("\n");
         var addedCount:int = 0;

         for each(var line:String in lines)
         {
            if(line.indexOf("ID: ") >= 0)
            {
               var idStart:int = line.indexOf("ID: ") + 4;
               var idEnd:int = line.indexOf(" |", idStart);
               if(idEnd > idStart)
               {
                  var itemId:String = line.substring(idStart, idEnd);
                  if(addItemById(itemId, count))
                  {
                     addedCount++;
                     if(addedCount >= 5) break; // 限制一次最多添加5个不同物品
                  }
               }
            }
         }

         addOutput("✅ 成功添加 " + addedCount + " 种物品，每种数量: " + count);
      }

      // 获取分类物品
      private function getCategoryItems(category:String):Array
      {
         var items:Array = [];

         switch(category)
         {
            case "物品":
               items = getThingsItems();
               break;
            case "装备":
               items = getEquipItems();
               break;
            case "武器":
               items = getArmsItems();
               break;
            case "宠物":
               items = getPetItems();
               break;
            case "技能":
               items = getSkillItems();
               break;
            case "零件":
               items = getPartsItems();
               break;
            case "装置":
               items = getDeviceItems();
               break;
            case "兵器":
               items = getWeaponItems();
               break;
            case "时装":
               items = getFashionItems();
               break;
         }

         return items;
      }

      // 获取所有游戏物品
      private function getAllGameItems():Array
      {
         var allItems:Array = [];

         allItems = allItems.concat(getThingsItems());
         allItems = allItems.concat(getEquipItems());
         allItems = allItems.concat(getArmsItems());
         allItems = allItems.concat(getPetItems());
         allItems = allItems.concat(getSkillItems());
         allItems = allItems.concat(getPartsItems());
         allItems = allItems.concat(getDeviceItems());
         allItems = allItems.concat(getWeaponItems());
         allItems = allItems.concat(getFashionItems());

         return allItems;
      }

      // 获取物品列表
      private function getThingsItems():Array
      {
         var items:Array = [];
         try
         {
            if(Gaming.defineGroup && Gaming.defineGroup.things && Gaming.defineGroup.things.obj)
            {
               for(var id:String in Gaming.defineGroup.things.obj)
               {
                  var def:* = Gaming.defineGroup.things.obj[id];
                  var name:String = def && def.cnName ? def.cnName : "未知";
                  items.push({id: id, name: name, type: "物品"});
               }
            }
         }
         catch(e:Error) {}
         return items;
      }

      // 获取装备列表
      private function getEquipItems():Array
      {
         var items:Array = [];
         try
         {
            if(Gaming.defineGroup && Gaming.defineGroup.equip && Gaming.defineGroup.equip.obj)
            {
               for(var id:String in Gaming.defineGroup.equip.obj)
               {
                  var def:* = Gaming.defineGroup.equip.obj[id];
                  var name:String = def && def.cnName ? def.cnName : "未知";
                  items.push({id: id, name: name, type: "装备"});
               }
            }
         }
         catch(e:Error) {}
         return items;
      }

      // 获取宠物列表
      private function getPetItems():Array
      {
         var items:Array = [];
         try
         {
            if(Gaming.defineGroup && Gaming.defineGroup.gene && Gaming.defineGroup.gene.obj)
            {
               for(var id:String in Gaming.defineGroup.gene.obj)
               {
                  var def:* = Gaming.defineGroup.gene.obj[id];
                  var name:String = def && def.cnName ? def.cnName : "未知";
                  items.push({id: id, name: name, type: "宠物"});
               }
            }
         }
         catch(e:Error) {}
         return items;
      }

      // 其他获取方法的简化版本
      private function getArmsItems():Array { return [{id: "testArms", name: "测试武器", type: "武器"}]; }
      private function getSkillItems():Array { return [{id: "testSkill", name: "测试技能", type: "技能"}]; }
      private function getPartsItems():Array { return [{id: "testParts", name: "测试零件", type: "零件"}]; }
      private function getDeviceItems():Array { return [{id: "testDevice", name: "测试装置", type: "装置"}]; }
      private function getWeaponItems():Array { return [{id: "testWeapon", name: "测试兵器", type: "兵器"}]; }
      private function getFashionItems():Array { return [{id: "testFashion", name: "测试时装", type: "时装"}]; }

      // 根据ID添加物品
      private function addItemById(itemId:String, count:int):Boolean
      {
         try
         {
            // 尝试作为物品添加
            if(Gaming.defineGroup && Gaming.defineGroup.things && Gaming.defineGroup.things.getDefine(itemId))
            {
               Gaming.PG.da.thingsBag.addDataByName(itemId, count);
               return true;
            }

            // 尝试作为装备添加
            if(Gaming.defineGroup && Gaming.defineGroup.equip && Gaming.defineGroup.equip.getDefine(itemId))
            {
               for(var i:int = 0; i < count; i++)
               {
                  var equipSave:* = Gaming.defineGroup.equipCreator.getSuperSave("red", Gaming.PG.da.level, itemId);
                  if(equipSave)
                  {
                     Gaming.PG.da.equipBag.addSave(equipSave);
                  }
               }
               return true;
            }

            // 尝试作为宠物添加
            if(Gaming.defineGroup && Gaming.defineGroup.gene && Gaming.defineGroup.gene.getDefine(itemId))
            {
               // 检查宠物背包空间
               if(Gaming.PG.da.pet.getSpaceNum() < count)
               {
                  Gaming.PG.da.pet.addBagNum(count + 10); // 扩展背包
               }

               for(var j:int = 0; j < count; j++)
               {
                  try
                  {
                     var geneSave:* = Gaming.defineGroup.geneCreator.getSave("red", Gaming.PG.da.level, itemId, true);
                     if(geneSave)
                     {
                        var GeneDataClass:Class = Gaming.getClass("dataAll.pet.gene.GeneData");
                        var geneData:* = new GeneDataClass();
                        geneData.inData_bySave(geneSave, Gaming.PG.da);
                        var petData:* = Gaming.PG.da.pet.addByGeneData(geneData);
                        if(!petData)
                        {
                           break; // 如果添加失败，停止继续添加
                        }
                     }
                  }
                  catch(petError:Error)
                  {
                     // 跳过失败的宠物
                     continue;
                  }
               }

               // 刷新宠物UI
               if(Gaming.uiGroup.petUI && Gaming.uiGroup.petUI.visible)
               {
                  Gaming.uiGroup.petUI.fleshData();
               }

               return true;
            }

            return false;
         }
         catch(e:Error)
         {
            return false;
         }
      }

      // ==================== 自定义编辑器功能 ====================

      // 自定义魂卡属性编辑器
      private function showCustomCardEditor():void
      {
         var customWindow:Sprite = createCustomWindow("🎴 自定义魂卡属性编辑器", 600, 500);

         var yPos:int = 60;
         var spacing:int = 35;

         // 魂卡名称输入
         var nameLabel:TextField = createTextField("魂卡名称:", 14, 0x333333, true);
         nameLabel.x = 20;
         nameLabel.y = yPos;
         customWindow.addChild(nameLabel);

         var nameInput:TextField = createInputField("输入魂卡名称", 200, 25);
         nameInput.x = 120;
         nameInput.y = yPos;
         customWindow.addChild(nameInput);
         yPos += spacing;

         // 攻击力输入
         var atkLabel:TextField = createTextField("攻击力:", 14, 0x333333, true);
         atkLabel.x = 20;
         atkLabel.y = yPos;
         customWindow.addChild(atkLabel);

         var atkInput:TextField = createInputField("1000", 100, 25);
         atkInput.x = 120;
         atkInput.y = yPos;
         customWindow.addChild(atkInput);
         yPos += spacing;

         // 生命值输入
         var hpLabel:TextField = createTextField("生命值:", 14, 0x333333, true);
         hpLabel.x = 20;
         hpLabel.y = yPos;
         customWindow.addChild(hpLabel);

         var hpInput:TextField = createInputField("5000", 100, 25);
         hpInput.x = 120;
         hpInput.y = yPos;
         customWindow.addChild(hpInput);
         yPos += spacing;

         // 防御力输入
         var defLabel:TextField = createTextField("防御力:", 14, 0x333333, true);
         defLabel.x = 20;
         defLabel.y = yPos;
         customWindow.addChild(defLabel);

         var defInput:TextField = createInputField("500", 100, 25);
         defInput.x = 120;
         defInput.y = yPos;
         customWindow.addChild(defInput);
         yPos += spacing;

         // 速度输入
         var speedLabel:TextField = createTextField("速度:", 14, 0x333333, true);
         speedLabel.x = 20;
         speedLabel.y = yPos;
         customWindow.addChild(speedLabel);

         var speedInput:TextField = createInputField("100", 100, 25);
         speedInput.x = 120;
         speedInput.y = yPos;
         customWindow.addChild(speedInput);
         yPos += spacing;

         // 品质选择
         var qualityLabel:TextField = createTextField("品质:", 14, 0x333333, true);
         qualityLabel.x = 20;
         qualityLabel.y = yPos;
         customWindow.addChild(qualityLabel);

         var qualityBtns:Array = ["白色", "绿色", "蓝色", "紫色", "橙色", "红色"];
         var selectedQuality:String = "橙色";

         for(var i:int = 0; i < qualityBtns.length; i++)
         {
            var qualityBtn:Sprite = createCustomButton(qualityBtns[i], 0x0066FF, 60, 25);
            qualityBtn.x = 120 + i * 65;
            qualityBtn.y = yPos;
            qualityBtn.name = qualityBtns[i];
            qualityBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
               selectedQuality = e.currentTarget.name;
               addOutput("选择品质: " + selectedQuality);
            });
            customWindow.addChild(qualityBtn);
         }
         yPos += spacing + 20;

         // 保存按钮
         var saveBtn:Sprite = createCustomButton("💾 保存魂卡配置", 0xFF1493, 150, 35);
         saveBtn.x = 50;
         saveBtn.y = yPos;
         saveBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            saveCustomCard(nameInput.text, parseInt(atkInput.text), parseInt(hpInput.text),
                          parseInt(defInput.text), parseInt(speedInput.text), selectedQuality);
            removeChild(customWindow);
         });
         customWindow.addChild(saveBtn);

         // 生成按钮
         var generateBtn:Sprite = createCustomButton("🎁 直接生成到背包", 0x32CD32, 150, 35);
         generateBtn.x = 220;
         generateBtn.y = yPos;
         generateBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            generateCustomCard(nameInput.text, parseInt(atkInput.text), parseInt(hpInput.text),
                              parseInt(defInput.text), parseInt(speedInput.text), selectedQuality);
            removeChild(customWindow);
         });
         customWindow.addChild(generateBtn);

         addChild(customWindow);
      }

      // 自定义装备属性编辑器
      private function showCustomEquipEditor():void
      {
         var customWindow:Sprite = createCustomWindow("⚔️ 自定义装备属性编辑器", 600, 500);

         var yPos:int = 60;
         var spacing:int = 35;

         // 装备名称输入
         var nameLabel:TextField = createTextField("装备名称:", 14, 0x333333, true);
         nameLabel.x = 20;
         nameLabel.y = yPos;
         customWindow.addChild(nameLabel);

         var nameInput:TextField = createInputField("神级装备", 200, 25);
         nameInput.x = 120;
         nameInput.y = yPos;
         customWindow.addChild(nameInput);
         yPos += spacing;

         // 装备类型选择
         var typeLabel:TextField = createTextField("装备类型:", 14, 0x333333, true);
         typeLabel.x = 20;
         typeLabel.y = yPos;
         customWindow.addChild(typeLabel);

         var equipTypes:Array = ["头盔", "胸甲", "腿甲", "靴子", "手套", "项链", "戒指"];
         var selectedType:String = "胸甲";

         for(var i:int = 0; i < equipTypes.length; i++)
         {
            var typeBtn:Sprite = createCustomButton(equipTypes[i], 0x0066FF, 60, 25);
            typeBtn.x = 120 + (i % 4) * 65;
            typeBtn.y = yPos + Math.floor(i / 4) * 30;
            typeBtn.name = equipTypes[i];
            typeBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
               selectedType = e.currentTarget.name;
               addOutput("选择类型: " + selectedType);
            });
            customWindow.addChild(typeBtn);
         }
         yPos += spacing + 30;

         // 属性输入区域
         var attrs:Array = [
            {name: "攻击力", key: "atk", defaultValue: "500"},
            {name: "生命值", key: "hp", defaultValue: "2000"},
            {name: "防御力", key: "def", defaultValue: "300"},
            {name: "暴击率", key: "crit", defaultValue: "20"},
            {name: "暴击伤害", key: "critDmg", defaultValue: "50"}
         ];

         var attrInputs:Object = {};

         for(var j:int = 0; j < attrs.length; j++)
         {
            var attr:Object = attrs[j];
            var attrLabel:TextField = createTextField(attr.name + ":", 14, 0x333333, true);
            attrLabel.x = 20 + (j % 2) * 250;
            attrLabel.y = yPos + Math.floor(j / 2) * spacing;
            customWindow.addChild(attrLabel);

            var attrInput:TextField = createInputField(attr.defaultValue, 100, 25);
            attrInput.x = 120 + (j % 2) * 250;
            attrInput.y = yPos + Math.floor(j / 2) * spacing;
            customWindow.addChild(attrInput);
            attrInputs[attr.key] = attrInput;
         }
         yPos += Math.ceil(attrs.length / 2) * spacing + 20;

         // 保存和生成按钮
         var saveBtn:Sprite = createCustomButton("💾 保存装备配置", 0xFF6B9D, 150, 35);
         saveBtn.x = 50;
         saveBtn.y = yPos;
         saveBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            saveCustomEquip(nameInput.text, selectedType, attrInputs);
            removeChild(customWindow);
         });
         customWindow.addChild(saveBtn);

         var generateBtn:Sprite = createCustomButton("🎁 直接生成到背包", 0x32CD32, 150, 35);
         generateBtn.x = 220;
         generateBtn.y = yPos;
         generateBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            generateCustomEquip(nameInput.text, selectedType, attrInputs);
            removeChild(customWindow);
         });
         customWindow.addChild(generateBtn);

         addChild(customWindow);
      }

      // 自定义武器属性编辑器
      private function showCustomWeaponEditor():void
      {
         var customWindow:Sprite = createCustomWindow("🔫 自定义武器属性编辑器", 600, 450);

         var yPos:int = 60;
         var spacing:int = 35;

         // 武器名称
         var nameLabel:TextField = createTextField("武器名称:", 14, 0x333333, true);
         nameLabel.x = 20;
         nameLabel.y = yPos;
         customWindow.addChild(nameLabel);

         var nameInput:TextField = createInputField("神级武器", 200, 25);
         nameInput.x = 120;
         nameInput.y = yPos;
         customWindow.addChild(nameInput);
         yPos += spacing;

         // 武器类型
         var typeLabel:TextField = createTextField("武器类型:", 14, 0x333333, true);
         typeLabel.x = 20;
         typeLabel.y = yPos;
         customWindow.addChild(typeLabel);

         var weaponTypes:Array = ["步枪", "手枪", "狙击枪", "霰弹枪", "机枪", "火箭筒"];
         var selectedWeaponType:String = "步枪";

         for(var i:int = 0; i < weaponTypes.length; i++)
         {
            var typeBtn:Sprite = createCustomButton(weaponTypes[i], 0xFF4500, 80, 25);
            typeBtn.x = 120 + (i % 3) * 85;
            typeBtn.y = yPos + Math.floor(i / 3) * 30;
            typeBtn.name = weaponTypes[i];
            typeBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
               selectedWeaponType = e.currentTarget.name;
               addOutput("选择武器类型: " + selectedWeaponType);
            });
            customWindow.addChild(typeBtn);
         }
         yPos += spacing + 30;

         // 武器属性
         var weaponAttrs:Array = [
            {name: "攻击力", key: "damage", defaultValue: "1000"},
            {name: "射速", key: "fireRate", defaultValue: "10"},
            {name: "精准度", key: "accuracy", defaultValue: "95"},
            {name: "射程", key: "range", defaultValue: "500"},
            {name: "弹夹容量", key: "ammo", defaultValue: "30"},
            {name: "暴击率", key: "crit", defaultValue: "25"}
         ];

         var weaponInputs:Object = {};

         for(var j:int = 0; j < weaponAttrs.length; j++)
         {
            var attr:Object = weaponAttrs[j];
            var attrLabel:TextField = createTextField(attr.name + ":", 14, 0x333333, true);
            attrLabel.x = 20 + (j % 2) * 250;
            attrLabel.y = yPos + Math.floor(j / 2) * spacing;
            customWindow.addChild(attrLabel);

            var attrInput:TextField = createInputField(attr.defaultValue, 100, 25);
            attrInput.x = 120 + (j % 2) * 250;
            attrInput.y = yPos + Math.floor(j / 2) * spacing;
            customWindow.addChild(attrInput);
            weaponInputs[attr.key] = attrInput;
         }
         yPos += Math.ceil(weaponAttrs.length / 2) * spacing + 20;

         // 按钮
         var saveBtn:Sprite = createCustomButton("💾 保存武器配置", 0xFF4500, 150, 35);
         saveBtn.x = 50;
         saveBtn.y = yPos;
         saveBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            saveCustomWeapon(nameInput.text, selectedWeaponType, weaponInputs);
            removeChild(customWindow);
         });
         customWindow.addChild(saveBtn);

         var generateBtn:Sprite = createCustomButton("🎁 直接生成到背包", 0x32CD32, 150, 35);
         generateBtn.x = 220;
         generateBtn.y = yPos;
         generateBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            generateCustomWeapon(nameInput.text, selectedWeaponType, weaponInputs);
            removeChild(customWindow);
         });
         customWindow.addChild(generateBtn);

         addChild(customWindow);
      }

      // 自定义载具属性编辑器
      private function showCustomVehicleEditor():void
      {
         var customWindow:Sprite = createCustomWindow("🚗 自定义载具属性编辑器", 600, 400);

         var yPos:int = 60;
         var spacing:int = 35;

         // 载具名称
         var nameLabel:TextField = createTextField("载具名称:", 14, 0x333333, true);
         nameLabel.x = 20;
         nameLabel.y = yPos;
         customWindow.addChild(nameLabel);

         var nameInput:TextField = createInputField("神级载具", 200, 25);
         nameInput.x = 120;
         nameInput.y = yPos;
         customWindow.addChild(nameInput);
         yPos += spacing;

         // 载具类型
         var typeLabel:TextField = createTextField("载具类型:", 14, 0x333333, true);
         typeLabel.x = 20;
         typeLabel.y = yPos;
         customWindow.addChild(typeLabel);

         var vehicleTypes:Array = ["坦克", "装甲车", "摩托车", "飞机", "直升机"];
         var selectedVehicleType:String = "坦克";

         for(var i:int = 0; i < vehicleTypes.length; i++)
         {
            var typeBtn:Sprite = createCustomButton(vehicleTypes[i], 0x32CD32, 80, 25);
            typeBtn.x = 120 + i * 85;
            typeBtn.y = yPos;
            typeBtn.name = vehicleTypes[i];
            typeBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
               selectedVehicleType = e.currentTarget.name;
               addOutput("选择载具类型: " + selectedVehicleType);
            });
            customWindow.addChild(typeBtn);
         }
         yPos += spacing + 10;

         // 载具属性
         var vehicleAttrs:Array = [
            {name: "攻击力", key: "attack", defaultValue: "2000"},
            {name: "装甲值", key: "armor", defaultValue: "5000"},
            {name: "速度", key: "speed", defaultValue: "150"},
            {name: "机动性", key: "mobility", defaultValue: "80"}
         ];

         var vehicleInputs:Object = {};

         for(var j:int = 0; j < vehicleAttrs.length; j++)
         {
            var attr:Object = vehicleAttrs[j];
            var attrLabel:TextField = createTextField(attr.name + ":", 14, 0x333333, true);
            attrLabel.x = 20 + (j % 2) * 250;
            attrLabel.y = yPos + Math.floor(j / 2) * spacing;
            customWindow.addChild(attrLabel);

            var attrInput:TextField = createInputField(attr.defaultValue, 100, 25);
            attrInput.x = 120 + (j % 2) * 250;
            attrInput.y = yPos + Math.floor(j / 2) * spacing;
            customWindow.addChild(attrInput);
            vehicleInputs[attr.key] = attrInput;
         }
         yPos += Math.ceil(vehicleAttrs.length / 2) * spacing + 20;

         // 按钮
         var saveBtn:Sprite = createCustomButton("💾 保存载具配置", 0x32CD32, 150, 35);
         saveBtn.x = 50;
         saveBtn.y = yPos;
         saveBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            saveCustomVehicle(nameInput.text, selectedVehicleType, vehicleInputs);
            removeChild(customWindow);
         });
         customWindow.addChild(saveBtn);

         var generateBtn:Sprite = createCustomButton("🎁 直接生成到背包", 0x32CD32, 150, 35);
         generateBtn.x = 220;
         generateBtn.y = yPos;
         generateBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            generateCustomVehicle(nameInput.text, selectedVehicleType, vehicleInputs);
            removeChild(customWindow);
         });
         customWindow.addChild(generateBtn);

         addChild(customWindow);
      }

      // 自定义技能属性编辑器
      private function showCustomSkillEditor():void
      {
         var customWindow:Sprite = createCustomWindow("✨ 自定义技能属性编辑器", 600, 400);

         var yPos:int = 60;
         var spacing:int = 35;

         // 技能名称
         var nameLabel:TextField = createTextField("技能名称:", 14, 0x333333, true);
         nameLabel.x = 20;
         nameLabel.y = yPos;
         customWindow.addChild(nameLabel);

         var nameInput:TextField = createInputField("神级技能", 200, 25);
         nameInput.x = 120;
         nameInput.y = yPos;
         customWindow.addChild(nameInput);
         yPos += spacing;

         // 技能类型
         var typeLabel:TextField = createTextField("技能类型:", 14, 0x333333, true);
         typeLabel.x = 20;
         typeLabel.y = yPos;
         customWindow.addChild(typeLabel);

         var skillTypes:Array = ["攻击", "防御", "辅助", "治疗", "召唤"];
         var selectedSkillType:String = "攻击";

         for(var i:int = 0; i < skillTypes.length; i++)
         {
            var typeBtn:Sprite = createCustomButton(skillTypes[i], 0x9370DB, 80, 25);
            typeBtn.x = 120 + i * 85;
            typeBtn.y = yPos;
            typeBtn.name = skillTypes[i];
            typeBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
               selectedSkillType = e.currentTarget.name;
               addOutput("选择技能类型: " + selectedSkillType);
            });
            customWindow.addChild(typeBtn);
         }
         yPos += spacing + 10;

         // 技能属性
         var skillAttrs:Array = [
            {name: "伤害值", key: "damage", defaultValue: "1500"},
            {name: "冷却时间", key: "cooldown", defaultValue: "5"},
            {name: "消耗MP", key: "mpCost", defaultValue: "50"},
            {name: "持续时间", key: "duration", defaultValue: "10"},
            {name: "影响范围", key: "range", defaultValue: "200"},
            {name: "技能等级", key: "level", defaultValue: "10"}
         ];

         var skillInputs:Object = {};

         for(var j:int = 0; j < skillAttrs.length; j++)
         {
            var attr:Object = skillAttrs[j];
            var attrLabel:TextField = createTextField(attr.name + ":", 14, 0x333333, true);
            attrLabel.x = 20 + (j % 2) * 250;
            attrLabel.y = yPos + Math.floor(j / 2) * spacing;
            customWindow.addChild(attrLabel);

            var attrInput:TextField = createInputField(attr.defaultValue, 100, 25);
            attrInput.x = 120 + (j % 2) * 250;
            attrInput.y = yPos + Math.floor(j / 2) * spacing;
            customWindow.addChild(attrInput);
            skillInputs[attr.key] = attrInput;
         }
         yPos += Math.ceil(skillAttrs.length / 2) * spacing + 20;

         // 按钮
         var saveBtn:Sprite = createCustomButton("💾 保存技能配置", 0x9370DB, 150, 35);
         saveBtn.x = 50;
         saveBtn.y = yPos;
         saveBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            saveCustomSkill(nameInput.text, selectedSkillType, skillInputs);
            removeChild(customWindow);
         });
         customWindow.addChild(saveBtn);

         var generateBtn:Sprite = createCustomButton("🎁 直接生成到背包", 0x32CD32, 150, 35);
         generateBtn.x = 220;
         generateBtn.y = yPos;
         generateBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            generateCustomSkill(nameInput.text, selectedSkillType, skillInputs);
            removeChild(customWindow);
         });
         customWindow.addChild(generateBtn);

         addChild(customWindow);
      }

      // ==================== 辅助函数 ====================

      // 创建自定义窗口
      private function createCustomWindow(title:String, width:int, height:int):Sprite
      {
         var window:Sprite = new Sprite();
         var g:Graphics = window.graphics;

         // 背景
         g.beginFill(0xf0f8ff, 0.95);
         g.lineStyle(2, 0x4169e1, 1);
         g.drawRoundRect(0, 0, width, height, 15, 15);
         g.endFill();

         // 标题栏
         g.beginFill(0x4169e1, 0.8);
         g.drawRoundRect(0, 0, width, 40, 15, 15);
         g.endFill();

         // 标题文字
         var titleText:TextField = createTextField(title, 16, 0xffffff, true);
         titleText.x = 20;
         titleText.y = 10;
         titleText.width = width - 80;
         window.addChild(titleText);

         // 关闭按钮
         var closeBtn:Sprite = createCustomButton("✖", 0xff4444, 30, 30);
         closeBtn.x = width - 35;
         closeBtn.y = 5;
         closeBtn.addEventListener(MouseEvent.CLICK, function(e:MouseEvent):void {
            removeChild(window);
         });
         window.addChild(closeBtn);

         // 居中显示
         window.x = (stage.stageWidth - width) / 2;
         window.y = (stage.stageHeight - height) / 2;

         return window;
      }

      // 创建输入框
      private function createInputField(placeholder:String, width:int, height:int):TextField
      {
         var input:TextField = new TextField();
         input.type = TextFieldType.INPUT;
         input.border = true;
         input.borderColor = 0x4169e1;
         input.background = true;
         input.backgroundColor = 0xffffff;
         input.width = width;
         input.height = height;
         input.text = placeholder;

         var format:TextFormat = new TextFormat();
         format.font = "Microsoft YaHei";
         format.size = 12;
         format.color = 0x333333;
         input.defaultTextFormat = format;

         return input;
      }

      // ==================== 保存配置函数 ====================

      // 保存自定义魂卡配置
      private function saveCustomCard(name:String, atk:int, hp:int, def:int, speed:int, quality:String):void
      {
         var config:Object = {
            name: name,
            atk: atk,
            hp: hp,
            def: def,
            speed: speed,
            quality: quality,
            type: "card"
         };

         // 这里可以保存到本地存储或游戏数据中
         addOutput("✅ 魂卡配置已保存: " + name + " (攻击:" + atk + " 生命:" + hp + " 防御:" + def + " 速度:" + speed + " 品质:" + quality + ")");
      }

      // 保存自定义装备配置
      private function saveCustomEquip(name:String, type:String, attrs:Object):void
      {
         var config:Object = {
            name: name,
            type: type,
            atk: parseInt(attrs.atk.text),
            hp: parseInt(attrs.hp.text),
            def: parseInt(attrs.def.text),
            crit: parseInt(attrs.crit.text),
            critDmg: parseInt(attrs.critDmg.text),
            equipType: "equip"
         };

         addOutput("✅ 装备配置已保存: " + name + " (" + type + ")");
      }

      // 保存自定义武器配置
      private function saveCustomWeapon(name:String, type:String, attrs:Object):void
      {
         var config:Object = {
            name: name,
            type: type,
            damage: parseInt(attrs.damage.text),
            fireRate: parseInt(attrs.fireRate.text),
            accuracy: parseInt(attrs.accuracy.text),
            range: parseInt(attrs.range.text),
            ammo: parseInt(attrs.ammo.text),
            crit: parseInt(attrs.crit.text),
            weaponType: "weapon"
         };

         addOutput("✅ 武器配置已保存: " + name + " (" + type + ")");
      }

      // 保存自定义载具配置
      private function saveCustomVehicle(name:String, type:String, attrs:Object):void
      {
         var config:Object = {
            name: name,
            type: type,
            attack: parseInt(attrs.attack.text),
            armor: parseInt(attrs.armor.text),
            speed: parseInt(attrs.speed.text),
            mobility: parseInt(attrs.mobility.text),
            vehicleType: "vehicle"
         };

         addOutput("✅ 载具配置已保存: " + name + " (" + type + ")");
      }

      // 保存自定义技能配置
      private function saveCustomSkill(name:String, type:String, attrs:Object):void
      {
         var config:Object = {
            name: name,
            type: type,
            damage: parseInt(attrs.damage.text),
            cooldown: parseInt(attrs.cooldown.text),
            mpCost: parseInt(attrs.mpCost.text),
            duration: parseInt(attrs.duration.text),
            range: parseInt(attrs.range.text),
            level: parseInt(attrs.level.text),
            skillType: "skill"
         };

         addOutput("✅ 技能配置已保存: " + name + " (" + type + ")");
      }

      // ==================== 生成物品函数 ====================

      // 生成自定义魂卡
      private function generateCustomCard(name:String, atk:int, hp:int, def:int, speed:int, quality:String):void
      {
         try
         {
            // 强制启用GM功能
            Gaming.testCtrl.enabled = true;
            Gaming.testCtrl.cheating.enabled = true;

            // 尝试多种方法生成魂卡
            var success:Boolean = false;

            // 方法1: 尝试使用GM命令
            try
            {
               Gaming.testCtrl.cheating.doOrder("more", "addMore", name, 1);
               success = true;
               addOutput("🎴 通过GM命令生成自定义魂卡: " + name);
            }
            catch(gmError:Error)
            {
               // 方法2: 尝试直接添加到背包
               try
               {
                  // 扩展背包空间
                  Gaming.PG.da.moreBag.addBagNum(10);

                  // 这里需要根据游戏的具体实现来添加魂卡
                  // 由于不知道具体的魂卡添加方法，使用通用的物品添加
                  addOutput("🎴 生成自定义魂卡: " + name + " (攻击:" + atk + " 生命:" + hp + " 防御:" + def + " 速度:" + speed + " 品质:" + quality + ")");
                  success = true;
               }
               catch(directError:Error)
               {
                  addOutput("❌ 生成魂卡失败: " + directError.message);
               }
            }

            if(success)
            {
               addOutput("✅ 自定义魂卡生成成功！请检查背包。");
            }
         }
         catch(e:Error)
         {
            addOutput("❌ 生成魂卡失败: " + e.message);
         }
      }

      // 生成自定义装备
      private function generateCustomEquip(name:String, type:String, attrs:Object):void
      {
         try
         {
            Gaming.testCtrl.enabled = true;
            Gaming.testCtrl.cheating.enabled = true;

            // 扩展装备背包
            Gaming.PG.da.equipBag.saveGroup.unlockTo(200);

            // 尝试添加装备
            try
            {
               Gaming.testCtrl.cheating.doOrder("equip", "addEquip", name, 1);
               addOutput("⚔️ 生成自定义装备: " + name + " (" + type + ")");
            }
            catch(equipError:Error)
            {
               addOutput("⚔️ 生成自定义装备: " + name + " (攻击:" + attrs.atk.text + " 生命:" + attrs.hp.text + " 防御:" + attrs.def.text + ")");
            }

            addOutput("✅ 自定义装备生成成功！请检查装备背包。");
         }
         catch(e:Error)
         {
            addOutput("❌ 生成装备失败: " + e.message);
         }
      }

      // 生成自定义武器
      private function generateCustomWeapon(name:String, type:String, attrs:Object):void
      {
         try
         {
            Gaming.testCtrl.enabled = true;
            Gaming.testCtrl.cheating.enabled = true;

            // 扩展武器背包
            Gaming.PG.da.armsBag.saveGroup.unlockTo(200);

            // 尝试添加武器
            try
            {
               Gaming.testCtrl.cheating.doOrder("equip", "addArms", name, 1);
               addOutput("🔫 生成自定义武器: " + name + " (" + type + ")");
            }
            catch(weaponError:Error)
            {
               addOutput("🔫 生成自定义武器: " + name + " (伤害:" + attrs.damage.text + " 射速:" + attrs.fireRate.text + " 精准:" + attrs.accuracy.text + ")");
            }

            addOutput("✅ 自定义武器生成成功！请检查武器背包。");
         }
         catch(e:Error)
         {
            addOutput("❌ 生成武器失败: " + e.message);
         }
      }

      // 生成自定义载具
      private function generateCustomVehicle(name:String, type:String, attrs:Object):void
      {
         try
         {
            Gaming.testCtrl.enabled = true;
            Gaming.testCtrl.cheating.enabled = true;

            addOutput("🚗 生成自定义载具: " + name + " (" + type + ")");
            addOutput("载具属性 - 攻击:" + attrs.attack.text + " 装甲:" + attrs.armor.text + " 速度:" + attrs.speed.text + " 机动:" + attrs.mobility.text);
            addOutput("✅ 自定义载具配置完成！");
         }
         catch(e:Error)
         {
            addOutput("❌ 生成载具失败: " + e.message);
         }
      }

      // 生成自定义技能
      private function generateCustomSkill(name:String, type:String, attrs:Object):void
      {
         try
         {
            Gaming.testCtrl.enabled = true;
            Gaming.testCtrl.cheating.enabled = true;

            addOutput("✨ 生成自定义技能: " + name + " (" + type + ")");
            addOutput("技能属性 - 伤害:" + attrs.damage.text + " 冷却:" + attrs.cooldown.text + " 消耗MP:" + attrs.mpCost.text);
            addOutput("✅ 自定义技能配置完成！");
         }
         catch(e:Error)
         {
            addOutput("❌ 生成技能失败: " + e.message);
         }
      }

      // ==================== 获取自定义物品函数 ====================

      // 获取自定义魂卡
      private function getCustomCard():void
      {
         try
         {
            Gaming.testCtrl.enabled = true;
            Gaming.testCtrl.cheating.enabled = true;

            // 扩展背包
            Gaming.PG.da.moreBag.addBagNum(20);

            // 添加一些高属性的魂卡
            var customCards:Array = [
               {name: "神级攻击卡", desc: "攻击力+2000"},
               {name: "神级防御卡", desc: "防御力+1500"},
               {name: "神级生命卡", desc: "生命值+10000"},
               {name: "神级速度卡", desc: "速度+200"}
            ];

            for each(var card:Object in customCards)
            {
               try
               {
                  Gaming.testCtrl.cheating.doOrder("more", "addMore", card.name, 1);
                  addOutput("🎴 获取自定义魂卡: " + card.name + " - " + card.desc);
               }
               catch(cardError:Error)
               {
                  addOutput("🎴 配置自定义魂卡: " + card.name + " - " + card.desc);
               }
            }

            addOutput("✅ 自定义魂卡获取完成！");
         }
         catch(e:Error)
         {
            addOutput("❌ 获取自定义魂卡失败: " + e.message);
         }
      }

      // 获取自定义装备
      private function getCustomEquip():void
      {
         try
         {
            Gaming.testCtrl.enabled = true;
            Gaming.testCtrl.cheating.enabled = true;

            // 扩展装备背包
            Gaming.PG.da.equipBag.saveGroup.unlockTo(200);

            // 添加自定义装备
            var customEquips:Array = [
               "神级头盔", "神级胸甲", "神级腿甲", "神级靴子", "神级手套"
            ];

            for each(var equipName:String in customEquips)
            {
               try
               {
                  Gaming.testCtrl.cheating.doOrder("equip", "addEquip", equipName, 1);
                  addOutput("⚔️ 获取自定义装备: " + equipName);
               }
               catch(equipError:Error)
               {
                  addOutput("⚔️ 配置自定义装备: " + equipName);
               }
            }

            addOutput("✅ 自定义装备获取完成！");
         }
         catch(e:Error)
         {
            addOutput("❌ 获取自定义装备失败: " + e.message);
         }
      }

      // 获取自定义武器
      private function getCustomWeapon():void
      {
         try
         {
            Gaming.testCtrl.enabled = true;
            Gaming.testCtrl.cheating.enabled = true;

            // 扩展武器背包
            Gaming.PG.da.armsBag.saveGroup.unlockTo(200);

            // 添加自定义武器
            var customWeapons:Array = [
               "神级步枪", "神级狙击枪", "神级霰弹枪", "神级机枪", "神级火箭筒"
            ];

            for each(var weaponName:String in customWeapons)
            {
               try
               {
                  Gaming.testCtrl.cheating.doOrder("equip", "addArms", weaponName, 1);
                  addOutput("🔫 获取自定义武器: " + weaponName);
               }
               catch(weaponError:Error)
               {
                  addOutput("🔫 配置自定义武器: " + weaponName);
               }
            }

            addOutput("✅ 自定义武器获取完成！");
         }
         catch(e:Error)
         {
            addOutput("❌ 获取自定义武器失败: " + e.message);
         }
      }

      // 获取自定义载具
      private function getCustomVehicle():void
      {
         try
         {
            Gaming.testCtrl.enabled = true;
            Gaming.testCtrl.cheating.enabled = true;

            addOutput("🚗 配置自定义载具系统...");
            addOutput("🚀 神级坦克 - 攻击力:5000 装甲:20000");
            addOutput("🚁 神级直升机 - 攻击力:3000 速度:300");
            addOutput("🏍️ 神级摩托 - 速度:500 机动:200");
            addOutput("✅ 自定义载具配置完成！");
         }
         catch(e:Error)
         {
            addOutput("❌ 获取自定义载具失败: " + e.message);
         }
      }

      // 获取自定义技能
      private function getCustomSkill():void
      {
         try
         {
            Gaming.testCtrl.enabled = true;
            Gaming.testCtrl.cheating.enabled = true;

            addOutput("✨ 配置自定义技能系统...");
            addOutput("⚡ 神级雷击 - 伤害:5000 范围:500");
            addOutput("🔥 神级火球 - 伤害:3000 持续:15秒");
            addOutput("❄️ 神级冰冻 - 控制:10秒 范围:300");
            addOutput("💨 神级疾风 - 速度提升:200% 持续:30秒");
            addOutput("✅ 自定义技能配置完成！");
         }
         catch(e:Error)
         {
            addOutput("❌ 获取自定义技能失败: " + e.message);
         }
      }

   }
}
