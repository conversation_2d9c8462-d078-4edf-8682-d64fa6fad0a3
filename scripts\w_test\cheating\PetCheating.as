package w_test.cheating
{
   import UI.pet.PetUI;
   import dataAll._app.space.craft.CraftData;
   import dataAll.pet.PetData;
   import dataAll.pet.gene.define.GeneDefine;
   
   public class PetCheating extends OneCheating
   {
      public function PetCheating()
      {
         super();
      }
      
      public function addNowCraftExp(str0:String, v0:int) : String
      {
         Gaming.PG.da.space.addNowCraftExp(v0);
         return "添加飞船经验值：" + v0;
      }
      
      public function setNowCraftLv(str0:String, v0:int) : String
      {
         var da0:CraftData = Gaming.PG.da.space.getNowCraft();
         if(<PERSON><PERSON>an(da0))
         {
            da0.setLevel(v0);
            return da0.getCnName() + " 设置飞船等级：" + v0;
         }
         return "";
      }
      
      public function clearAllPet(str0:String, v0:int) : String
      {
         Gaming.PG.da.pet.clearData();
         return "清除所有宠物";
      }
      
      public function setPetLv(str0:String, v0:int) : String
      {
         var da0:PetData = PetUI.getNowData();
         if(da0 is PetData)
         {
            da0.base.save.level = v0;
         }
         return "设置当前尸宠等级为：" + v0;
      }
      
      public function addNowGenePro(str0:String, v0:int) : String
      {
         var d0:GeneDefine = Gaming.uiGroup.petUI.bookBoard.nowGeneDefine;
         if(Boolean(d0))
         {
            Gaming.PG.da.pet.saveGroup.map.setDropPro(d0.name,v0 / 100,true);
         }
         return "添加" + d0.cnName + "基因体掉落概率：" + v0 / 100;
      }

      public function addAllPet(str0:String, v0:int) : String
      {
         var addedPets:int = 0;
         var failedPets:int = 0;

         try
         {
            // 扩展宠物背包空间
            Gaming.PG.da.pet.addBagNum(200);

            // 获取所有正常的宠物名称
            var normalPetNames:Array = Gaming.defineGroup.gene.getNormalNameArr();

            // 如果没有找到正常宠物，使用备用列表
            if(normalPetNames.length == 0)
            {
               normalPetNames = [
                  "BoomSkull", "IronChief", "Lake", "FightWolf", "ZombieWolf",
                  "ZombieKing", "ZombieCleaver", "IronChiefSecond", "Laer",
                  "PetLake", "BoomWolf", "ZombieChief"
               ];
            }

            for each(var petName:String in normalPetNames)
            {
               try
               {
                  // 确保有足够的空间
                  if(Gaming.PG.da.pet.getSpaceNum() <= 0)
                  {
                     Gaming.PG.da.pet.addBagNum(10);
                  }

                  // 检查基因定义是否存在
                  var geneDefine:* = Gaming.defineGroup.gene.getDefine(petName);
                  if(!geneDefine)
                  {
                     failedPets++;
                     continue;
                  }

                  // 创建基因保存数据
                  var geneSave:* = Gaming.defineGroup.geneCreator.getSave("red", Gaming.PG.da.level, petName, true);
                  if(geneSave)
                  {
                     // 创建基因数据
                     var GeneDataClass:Class = Gaming.getClass("dataAll.pet.gene.GeneData");
                     var geneData:* = new GeneDataClass();
                     geneData.inData_bySave(geneSave, Gaming.PG.da);

                     // 添加到宠物背包
                     var petData:* = Gaming.PG.da.pet.addByGeneData(geneData);
                     if(petData)
                     {
                        addedPets++;
                     }
                     else
                     {
                        failedPets++;
                     }
                  }
                  else
                  {
                     failedPets++;
                  }
               }
               catch(petError:Error)
               {
                  failedPets++;
               }
            }

            // 刷新宠物UI
            try
            {
               if(Gaming.uiGroup.petUI && Gaming.uiGroup.petUI.visible)
               {
                  Gaming.uiGroup.petUI.fleshData();
               }
            }
            catch(uiError:Error)
            {
               // 忽略UI刷新错误
            }
         }
         catch(e:Error)
         {
            return "添加所有宠物失败: " + e.message;
         }

         return "添加所有宠物：成功 " + addedPets + " 只，失败 " + failedPets + " 只";
      }
   }
}

